# Examples

This directory contains example programs demonstrating how to use the OSM-PBF-LanceDB tool and query the generated databases.

## Available Examples

### `queries.rs`
Comprehensive query examples for OSM data using the optimized schema. Demonstrates:
- Finding restaurants using category and type fields
- Spatial queries using the geo field
- Analyzing highway networks
- Searching for features with specific names
- Complex aggregation and analytics queries
- Working with extratags for detailed tag analysis

### `storage_benchmark.rs`
Storage efficiency analysis tool that:
- Analyzes database size and compression ratios
- Provides storage optimization recommendations
- Compares different schema approaches
- Shows performance trade-offs

### `check_tables.rs`
Simple utility to list all available tables in a LanceDB database.

### `validate_output.rs`
Validation tool that:
- Tests database connectivity
- Verifies table schemas
- Performs basic data integrity checks
- Validates the OSM PBF to LanceDB pipeline output

## Running Examples

To run any example:

```bash
# Compile and run an example
cargo run --example queries

# Or run directly with cargo
cargo run --bin example_name
```

## Database Schema

All examples use the storage-optimized schema with compressed table names:
- `osm_nodes_compressed` - Point features (amenities, shops, etc.)
- `osm_ways_compressed` - Linear features (roads, rivers, etc.)
- `osm_relations_compressed` - Complex relationships (routes, boundaries, etc.)

## Prerequisites

Before running the examples, ensure you have:
1. Generated a LanceDB database using the main tool
2. The database is accessible at the expected path (usually `./test_output_monaco`)

Example database generation:
```bash
# Download test data
wget https://download.geofabrik.de/europe/monaco-latest.osm.pbf

# Generate LanceDB database
cargo run --release -- --input monaco-latest.osm.pbf --output ./test_output_monaco
```
