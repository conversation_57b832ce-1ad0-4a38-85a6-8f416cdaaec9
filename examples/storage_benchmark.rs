use anyhow::Result;

/// Benchmark storage efficiency between different schema approaches
#[tokio::main]
async fn main() -> Result<()> {
    println!("🔍 OSM-PBF-LanceDB Storage Analysis");
    println!("=====================================\n");
    
    // Analyze current optimized output
    analyze_storage_efficiency("./test_output_monaco", "Small Dataset (Monaco)").await?;
    analyze_storage_efficiency("./test_output_large", "Large Dataset (Malaysia-Singapore-Brunei)").await?;
    
    // Show optimization recommendations
    show_optimization_recommendations();
    
    Ok(())
}

async fn analyze_storage_efficiency(db_path: &str, dataset_name: &str) -> Result<()> {
    println!("📊 Analyzing: {}", dataset_name);
    println!("Database: {}\n", db_path);
    
    // Check if database exists
    if !std::path::Path::new(db_path).exists() {
        println!("❌ Database not found: {}\n", db_path);
        return Ok(());
    }
    
    // Connect to database
    let db = lancedb::connect(db_path).execute().await?;
    let table_names = db.table_names().execute().await?;
    
    let mut total_size = 0u64;
    let mut total_records = 0u64;
    
    for table_name in &table_names {
        if let Ok(table) = db.open_table(table_name).execute().await {
            let record_count = table.count_rows(None).await.unwrap_or(0);
            
            // Get table size (approximate)
            let table_path = format!("{}/{}.lance", db_path, table_name);
            let table_size = get_directory_size(&table_path).unwrap_or(0);
            
            total_size += table_size;
            total_records += record_count as u64;
            
            println!("📋 Table: {}", table_name);
            println!("   Records: {}", record_count);
            println!("   Size: {}", format_bytes(table_size));
            println!("   Bytes/Record: {:.1}", table_size as f64 / record_count as f64);
            println!();
        }
    }
    
    println!("📈 Summary for {}:", dataset_name);
    println!("   Total Records: {}", total_records);
    println!("   Total Size: {}", format_bytes(total_size));
    println!("   Average Bytes/Record: {:.1}", total_size as f64 / total_records as f64);
    
    // Calculate efficiency metrics
    if dataset_name.contains("Monaco") {
        let pbf_size = 1_500_000u64; // Approximate Monaco PBF size
        let expansion_factor = total_size as f64 / pbf_size as f64;
        println!("   Expansion Factor: {:.1}x", expansion_factor);
    } else if dataset_name.contains("Malaysia") {
        let pbf_size = 234_000_000u64; // 234 MB
        let expansion_factor = total_size as f64 / pbf_size as f64;
        println!("   Expansion Factor: {:.1}x", expansion_factor);
    }
    
    println!("\n{}\n", "=".repeat(50));
    
    Ok(())
}

fn get_directory_size(path: &str) -> Result<u64> {
    let mut total_size = 0;
    
    if let Ok(entries) = std::fs::read_dir(path) {
        for entry in entries.flatten() {
            let metadata = entry.metadata()?;
            if metadata.is_file() {
                total_size += metadata.len();
            } else if metadata.is_dir() {
                total_size += get_directory_size(&entry.path().to_string_lossy())?;
            }
        }
    }
    
    Ok(total_size)
}

fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

fn show_optimization_recommendations() {
    println!("🚀 Storage Optimization Recommendations");
    println!("=======================================\n");
    
    println!("1. 📦 Enable Compression (Expected: 60-70% reduction)");
    println!("   - Use ZSTD compression for nodes (best ratio)");
    println!("   - Use LZ4 compression for ways (faster)");
    println!("   - Configure compression level 2-3 for balance");
    println!();
    
    println!("2. 🔧 Optimize Data Types (Expected: 20-30% reduction)");
    println!("   - Use Float32 instead of Float64 for coordinates");
    println!("   - Use UInt64 instead of Int64 for OSM IDs");
    println!("   - Use dictionary encoding for categories/types");
    println!("   - Use UInt8 enum for relation member types");
    println!();
    
    println!("3. 📊 Batch Size Optimization (Expected: 10-15% reduction)");
    println!("   - Nodes: 32MB batches (better compression)");
    println!("   - Ways: 64MB batches (handle large node lists)");
    println!("   - Relations: 16MB batches (complex structures)");
    println!();
    
    println!("4. 🎯 String Optimization (Expected: 15-25% reduction)");
    println!("   - Use string interning for common values");
    println!("   - Dictionary encode tag keys and values");
    println!("   - Optimize name field storage");
    println!();
    
    println!("📈 Expected Results:");
    println!("   Current: 13.3x expansion (224 MB → 2.9 GB)");
    println!("   Optimized: 2.3x expansion (224 MB → 520 MB)");
    println!("   Total Reduction: 82% storage savings");
    println!();
    
    println!("⚡ Performance Trade-offs:");
    println!("   ✅ Read Speed: 5-15% faster (less I/O)");
    println!("   ⚠️  Write Speed: 10-20% slower (compression)");
    println!("   ✅ Memory: Slightly higher during compression");
    println!("   ✅ Query Performance: Maintained or improved");
    println!();
    
    println!("🔧 Implementation Priority:");
    println!("   1. High Impact: Enable compression (easy, 65% reduction)");
    println!("   2. Medium Impact: Optimize data types (moderate effort)");
    println!("   3. Low Impact: Batch optimization (easy, small gain)");
    println!("   4. Complex: String optimization (high effort)");
    println!();
    
    println!("📝 Next Steps:");
    println!("   1. Implement compression in LanceDBManager");
    println!("   2. Create storage-optimized schema variant");
    println!("   3. Add compression configuration options");
    println!("   4. Benchmark optimized vs current implementation");
    println!("   5. Update documentation with storage guidance");
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_format_bytes() {
        assert_eq!(format_bytes(512), "512 B");
        assert_eq!(format_bytes(1024), "1.0 KB");
        assert_eq!(format_bytes(1536), "1.5 KB");
        assert_eq!(format_bytes(1048576), "1.0 MB");
        assert_eq!(format_bytes(1073741824), "1.0 GB");
    }
}
