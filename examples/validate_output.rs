use anyhow::Result;
use lancedb::connect;
use lancedb::query::ExecutableQuery;
use futures::stream::StreamExt;

#[tokio::main]
async fn main() -> Result<()> {
    println!("🔍 Validating OSM PBF to LanceDB pipeline output...");
    
    // Connect to the generated database
    let db = connect("./test_output_monaco").execute().await?;
    
    // Test nodes table
    println!("\n📍 Testing nodes table...");
    let nodes_table = db.open_table("osm_nodes_compressed").execute().await?;
    let node_count = nodes_table.count_rows(None).await?;
    println!("✅ Nodes count: {}", node_count);
    
    // Test ways table
    println!("\n🛣️  Testing ways table...");
    let ways_table = db.open_table("osm_ways_compressed").execute().await?;
    let way_count = ways_table.count_rows(None).await?;
    println!("✅ Ways count: {}", way_count);
    
    // Test relations table
    println!("\n🔗 Testing relations table...");
    let relations_table = db.open_table("osm_relations_compressed").execute().await?;
    let relation_count = relations_table.count_rows(None).await?;
    println!("✅ Relations count: {}", relation_count);
    
    // Test a simple query on nodes
    println!("\n🔍 Testing sample query on nodes...");
    let mut stream = nodes_table.query().execute().await?;

    let mut total_rows = 0;
    let mut batch_count = 0;
    while let Some(batch_result) = stream.next().await {
        let batch = batch_result?;
        total_rows += batch.num_rows();
        batch_count += 1;

        if batch_count == 1 {
            println!("📊 Sample batch: {} rows, {} columns", batch.num_rows(), batch.num_columns());

            // Print column names
            print!("📋 Columns: ");
            for field in batch.schema().fields() {
                print!("{} ", field.name());
            }
            println!();
        }

        // Only process first few batches for validation
        if batch_count >= 3 {
            break;
        }
    }
    println!("✅ Retrieved {} rows from {} batches", total_rows, batch_count);

    // Test a query on ways
    println!("\n🛣️  Testing sample query on ways...");
    let mut ways_stream = ways_table.query().execute().await?;

    let mut ways_sample_count = 0;
    let mut ways_batch_count = 0;
    while let Some(batch_result) = ways_stream.next().await {
        let batch = batch_result?;
        ways_sample_count += batch.num_rows();
        ways_batch_count += 1;

        // Only process first batch for validation
        if ways_batch_count >= 1 {
            break;
        }
    }
    println!("✅ Retrieved {} sample ways from {} batches", ways_sample_count, ways_batch_count);
    
    // Summary
    println!("\n🎉 Validation Summary:");
    println!("   📍 Nodes: {}", node_count);
    println!("   🛣️  Ways: {}", way_count);
    println!("   🔗 Relations: {}", relation_count);
    println!("   📊 Total OSM objects: {}", node_count + way_count + relation_count);
    
    // Verify the counts match the pipeline output
    let expected_total = 46860; // From the pipeline output
    let actual_total = node_count + way_count + relation_count;
    
    if actual_total == expected_total {
        println!("✅ Data integrity check PASSED: {} objects processed correctly", actual_total);
    } else {
        println!("❌ Data integrity check FAILED: expected {}, got {}", expected_total, actual_total);
        return Err(anyhow::anyhow!("Data count mismatch"));
    }
    
    println!("\n🎯 All validation tests PASSED! The OSM PBF to LanceDB pipeline is working correctly.");
    
    Ok(())
}
