use anyhow::Result;
use lancedb::connect;

#[tokio::main]
async fn main() -> Result<()> {
    println!("🔍 Checking available tables in test_output_monaco...");
    
    // Connect to the generated database
    let db = connect("./test_output_monaco").execute().await?;
    
    // List all tables
    let table_names = db.table_names().execute().await?;
    
    println!("📋 Available tables:");
    for table_name in &table_names {
        println!("  - {}", table_name);
    }
    
    Ok(())
}
