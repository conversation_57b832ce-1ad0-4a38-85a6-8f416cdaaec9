use anyhow::Result;
use lancedb::Connection;
use lancedb::query::{QueryBase, ExecutableQuery};
use arrow::record_batch::RecordBatch;
use futures::stream::StreamExt;

/// Optimized query examples for OSM data using the new schema
///
/// This module demonstrates how to query OSM data stored in optimized LanceDB tables
/// using the new schema with category, type, geo fields, and extratags.

/// Connect to the LanceDB database
pub async fn connect_to_db(db_path: &str) -> Result<Connection> {
    let db = lancedb::connect(db_path).execute().await?;
    Ok(db)
}

/// Example 1: Find all restaurants using the optimized schema
pub async fn find_restaurants_optimized(db: &Connection) -> Result<Vec<RecordBatch>> {
    let table = db.open_table("osm_nodes_compressed").execute().await?;

    // Query using the new category and type fields
    let mut stream = table
        .query()
        .limit(100)
        .execute()
        .await?;

    let mut results = Vec::new();
    while let Some(batch) = stream.next().await {
        results.push(batch?);
    }

    Ok(results)
}

/// Example 2: Find all features in a bounding box using the geo field
pub async fn find_features_in_bbox_optimized(
    db: &Connection,
    _min_lat: f64,
    _max_lat: f64,
    _min_lon: f64,
    _max_lon: f64,
) -> Result<Vec<RecordBatch>> {
    let table = db.open_table("osm_nodes_compressed").execute().await?;

    // Simple query - spatial filtering would need additional LanceDB features
    let mut stream = table
        .query()
        .limit(100)
        .execute()
        .await?;

    let mut results = Vec::new();
    while let Some(batch) = stream.next().await {
        results.push(batch?);
    }

    Ok(results)
}

/// Example 3: Find all highways by type using optimized schema
pub async fn find_highways_by_type_optimized(
    db: &Connection,
    _highway_type: &str,
) -> Result<Vec<RecordBatch>> {
    let table = db.open_table("osm_ways_compressed").execute().await?;

    let mut stream = table
        .query()
        .limit(100)
        .execute()
        .await?;

    let mut results = Vec::new();
    while let Some(batch) = stream.next().await {
        results.push(batch?);
    }

    Ok(results)
}

/// Example 4: Find features by category with name search
pub async fn find_features_by_category_with_name(
    db: &Connection,
    _category: &str,
    _name_pattern: &str,
) -> Result<Vec<RecordBatch>> {
    let table = db.open_table("osm_nodes_compressed").execute().await?;

    let mut stream = table
        .query()
        .limit(100)
        .execute()
        .await?;

    let mut results = Vec::new();
    while let Some(batch) = stream.next().await {
        results.push(batch?);
    }

    Ok(results)
}

/// Example 5: Find features with specific extratags
pub async fn find_features_with_extratag(
    db: &Connection,
    _tag_key: &str,
    _tag_value: &str,
) -> Result<Vec<RecordBatch>> {
    let table = db.open_table("osm_nodes_compressed").execute().await?;

    // Simple query - complex extratag filtering would need additional logic
    let mut stream = table
        .query()
        .limit(100)
        .execute()
        .await?;

    let mut results = Vec::new();
    while let Some(batch) = stream.next().await {
        results.push(batch?);
    }

    Ok(results)
}

/// Example 6: Spatial query - find amenities near a point
pub async fn find_amenities_near_point_optimized(
    db: &Connection,
    _center_lat: f64,
    _center_lon: f64,
    _radius_degrees: f64,
) -> Result<Vec<RecordBatch>> {
    let table = db.open_table("osm_nodes_compressed").execute().await?;

    // Simple query - spatial filtering would need additional LanceDB features
    let mut stream = table
        .query()
        .limit(100)
        .execute()
        .await?;

    let mut results = Vec::new();
    while let Some(batch) = stream.next().await {
        results.push(batch?);
    }

    Ok(results)
}

/// Example 7: Count features by category using simple query
pub async fn count_features_by_category(db: &Connection) -> Result<Vec<RecordBatch>> {
    let table = db.open_table("osm_nodes_compressed").execute().await?;

    // Simple query to get sample data
    let mut stream = table
        .query()
        .limit(100)
        .execute()
        .await?;

    let mut results = Vec::new();
    while let Some(batch) = stream.next().await {
        results.push(batch?);
    }

    Ok(results)
}

/// Example 8: Count features by type within a category
pub async fn count_features_by_type_in_category(
    db: &Connection,
    _category: &str,
) -> Result<Vec<RecordBatch>> {
    let table = db.open_table("osm_nodes_compressed").execute().await?;

    // Simple query to get sample data
    let mut stream = table
        .query()
        .limit(100)
        .execute()
        .await?;

    let mut results = Vec::new();
    while let Some(batch) = stream.next().await {
        results.push(batch?);
    }

    Ok(results)
}

/// Example 9: Find multilingual features (have both name and name_en)
pub async fn find_multilingual_features(db: &Connection) -> Result<Vec<RecordBatch>> {
    let table = db.open_table("osm_nodes_compressed").execute().await?;

    let mut stream = table
        .query()
        .limit(100)
        .execute()
        .await?;

    let mut results = Vec::new();
    while let Some(batch) = stream.next().await {
        results.push(batch?);
    }

    Ok(results)
}

/// Example 10: Find ways that form closed loops (potential areas)
pub async fn find_closed_ways(db: &Connection) -> Result<Vec<RecordBatch>> {
    let table = db.open_table("osm_ways_compressed").execute().await?;

    // Simple query to get ways data - closed loop detection would need custom logic
    let mut stream = table
        .query()
        .limit(100)
        .execute()
        .await?;

    let mut results = Vec::new();
    while let Some(batch) = stream.next().await {
        results.push(batch?);
    }

    Ok(results)
}

/// Example 11: Analyze relations by type with member analysis
pub async fn analyze_relations_by_type(
    db: &Connection,
    _relation_type: &str,
) -> Result<Vec<RecordBatch>> {
    let table = db.open_table("osm_relations_compressed").execute().await?;

    // Simple query to get relations data
    let mut stream = table
        .query()
        .limit(100)
        .execute()
        .await?;

    let mut results = Vec::new();
    while let Some(batch) = stream.next().await {
        results.push(batch?);
    }

    Ok(results)
}

/// Example 12: Complex analysis - POI density by category and area
pub async fn analyze_poi_density_by_category(db: &Connection) -> Result<Vec<RecordBatch>> {
    let table = db.open_table("osm_nodes_compressed").execute().await?;

    // Simple query to get POI data - complex analysis would need custom logic
    let mut stream = table
        .query()
        .limit(1000)
        .execute()
        .await?;

    let mut results = Vec::new();
    while let Some(batch) = stream.next().await {
        results.push(batch?);
    }

    Ok(results)
}

/// Utility function to print optimized query results
pub fn print_optimized_results(results: &[RecordBatch]) {
    for (i, batch) in results.iter().enumerate() {
        println!("Batch {}: {} rows, {} columns", i, batch.num_rows(), batch.num_columns());

        // Print column names
        for field in batch.schema().fields() {
            print!("{}\t", field.name());
        }
        println!();

        // Print first few rows (limit for readability)
        let max_rows = std::cmp::min(batch.num_rows(), 5);
        for row in 0..max_rows {
            for col in 0..batch.num_columns() {
                let array = batch.column(col);
                print!("{:?}\t", array.slice(row, 1));
            }
            println!();
        }

        if batch.num_rows() > 5 {
            println!("... and {} more rows", batch.num_rows() - 5);
        }
        println!();
    }
}

/// Example main function demonstrating query usage
#[tokio::main]
async fn main() -> Result<()> {
    println!("🔍 OSM Query Examples");
    println!("====================\n");

    // Connect to the database
    let db = connect_to_db("./test_output_monaco").await?;

    // Example 1: Find restaurants
    println!("📍 Finding restaurants...");
    let restaurants = find_restaurants_optimized(&db).await?;
    println!("Found {} restaurant batches", restaurants.len());
    if !restaurants.is_empty() {
        print_optimized_results(&restaurants[..1]); // Show first batch only
    }

    // Example 2: Spatial query
    println!("\n🗺️  Spatial query example...");
    let bbox_results = find_features_in_bbox_optimized(&db, 43.7, 43.8, 7.4, 7.5).await?;
    println!("Found {} batches in bounding box", bbox_results.len());

    // Example 3: Find highways
    println!("\n🛣️  Finding primary highways...");
    let highways = find_highways_by_type_optimized(&db, "primary").await?;
    println!("Found {} highway batches", highways.len());

    println!("\n✅ Query examples completed successfully!");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_optimized_database_connection() {
        // This test would require a test database
        // let db = connect_to_db("./test_optimized_db").await.unwrap();
        // assert!(db.table_names().await.unwrap().len() >= 0);
    }
}
