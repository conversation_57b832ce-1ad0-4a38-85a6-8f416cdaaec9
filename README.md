# osm-pbf-lancedb

A high-performance Rust tool for importing OpenStreetMap (OSM) PBF files directly into LanceDB, a modern vector database built on Apache Arrow. This tool efficiently processes OSM data and stores it in optimized LanceDB tables for fast querying and analysis.

## Features

- **Fast PBF Processing**: Multi-threaded processing of OSM PBF files with configurable worker threads
- **LanceDB Integration**: Direct import into LanceDB tables with optimized schemas for each OSM entity type
- **Type-Specific Tables**: Separate tables for nodes, ways, and relations with appropriate schemas
- **Memory Efficient**: Configurable batch sizes and memory management for processing large files
- **S3 Support**: Read PBF files from local filesystem or AWS S3
- **Optimized Storage**: Automatic table optimization after import for better query performance
- **Rich Metadata**: Preserves all OSM tags, timestamps, user information, and version history
- **Smart Filtering**: Configurable preprocessing and filtering system to import only relevant data
- **Multilingual Support**: Flexible name tag patterns supporting international datasets
- **Data Quality**: Coordinate validation and customizable filtering rules

## Installation

### Prerequisites

- [Rust](https://www.rust-lang.org/tools/install) (1.70 or later)
- Git

### Build from Source

```bash
git clone https://github.com/your-repo/osm-pbf-lancedb.git
cd osm-pbf-lancedb
cargo build --release
```

The compiled binary will be available at `target/release/osm-pbf-lancedb`.

## Usage

### Basic Usage

```bash
# Import a local PBF file with default filtering
./osm-pbf-lancedb --input your.osm.pbf --output ./lancedb

# Import all data without filtering
./osm-pbf-lancedb --input your.osm.pbf --output ./lancedb --full

# Import from S3 (requires AWS credentials in environment)
./osm-pbf-lancedb --input s3://bucket/path/to/file.osm.pbf --output ./lancedb
```

### Advanced Options

```bash
./osm-pbf-lancedb \
  --input your.osm.pbf \
  --output ./lancedb \
  --worker-threads 8 \
  --input-buffer-size-mb 16 \
  --record-batch-target-mb 100 \
  --file-target-mb 500 \
  --full  # Optional: bypass all filtering
```

### Command Line Options

- `--input, -i`: Path to input PBF file (local path or S3 URI)
- `--output, -o`: Path to LanceDB database directory (default: `./lancedb`)
- `--worker-threads`: Number of worker threads (default: CPU count)
- `--input-buffer-size-mb`: Input buffer size in MB (default: 8MB)
- `--record-batch-target-mb`: Target record batch size in MB (default: auto-calculated)
- `--file-target-mb`: Target file size for LanceDB writes in MB (default: 500MB)
- `--full`: Import all OSM data without any filtering or preprocessing

### Supported Input Sources

- **Local filesystem**: Any local `.osm.pbf` file
- **AWS S3**: S3 URIs with authentication from environment variables (see [object_store docs](https://docs.rs/object_store/latest/object_store/aws/struct.AmazonS3Builder.html))

### Output Structure

The tool creates a LanceDB database with three optimized tables:

```
lancedb/
├── osm_nodes_compressed.lance      # Point features (POIs, addresses, etc.)
├── osm_ways_compressed.lance       # Linear features (roads, boundaries, etc.)
└── osm_relations_compressed.lance  # Complex relationships (routes, multipolygons, etc.)
```

## Data Filtering

The tool includes a comprehensive filtering system to import only relevant OSM data, reducing database size and improving query performance.

### Default Filtering Rules

By default, OSM records are filtered to include only those that meet **ALL** of the following criteria:

1. **Valid Geographic Coordinates**: Contains valid latitude and longitude values (nodes only)
2. **Name Tags**: Contains at least one name tag (`name`, `name:*`, etc.)

### Configuration

Filtering is configured via the `osm_tag_config.toml` file:

```toml
[filter]
enabled = true
require_coordinates = true
require_name_tags = true
name_tag_patterns = ["name", "name:*"]
```

### Bypass Filtering

Use the `--full` flag to import all data without filtering:

```bash
./osm-pbf-lancedb --input data.osm.pbf --output ./lancedb --full
```

### Example Configurations

The `examples/filter_configs/` directory contains several pre-configured filtering options:

- `strict_filtering.toml` - Only standard name tags
- `multilingual_filtering.toml` - Multiple language name variants
- `coordinates_only.toml` - Coordinate validation only
- `no_filtering.toml` - Import everything

For detailed filtering documentation, see [FILTERING.md](FILTERING.md).

## Data Schema

The tool uses an optimized schema designed for high-performance queries and reduced storage overhead:
#### Nodes Table (`osm_nodes_compressed`)
- `osm_id`: Unique OSM identifier (UInt64)
- `name`: Value from 'name' tag (Dictionary<UInt32, String>, nullable)
- `name_en`: Value from 'name:en' tag (Dictionary<UInt32, String>, nullable)
- `geo`: Geographic coordinates as [longitude, latitude] array (FixedSizeList<Float32, 2>)
- `category`: Extracted from highest priority tag key (Dictionary<UInt16, String>, nullable)
- `type`: Extracted from highest priority tag value (Dictionary<UInt16, String>, nullable)
- `extratags`: Remaining OSM tags as List<Struct{key, value}> (nullable)

#### Ways Table (`osm_ways_compressed`)
- `osm_id`: Unique OSM identifier (UInt64)
- `name`: Value from 'name' tag (Dictionary<UInt32, String>, nullable)
- `name_en`: Value from 'name:en' tag (Dictionary<UInt32, String>, nullable)
- `category`: Extracted from highest priority tag key (Dictionary<UInt16, String>, nullable)
- `type`: Extracted from highest priority tag value (Dictionary<UInt16, String>, nullable)
- `extratags`: Remaining OSM tags as List<Struct{key, value}> (nullable)
- `nds`: Array of node reference IDs (List<UInt64>, nullable)

#### Relations Table (`osm_relations_compressed`)
- `osm_id`: Unique OSM identifier (UInt64)
- `name`: Value from 'name' tag (Dictionary<UInt32, String>, nullable)
- `name_en`: Value from 'name:en' tag (Dictionary<UInt32, String>, nullable)
- `category`: Extracted from highest priority tag key (Dictionary<UInt16, String>, nullable)
- `type`: Extracted from highest priority tag value (Dictionary<UInt16, String>, nullable)
- `extratags`: Remaining OSM tags as List<Struct{key, value}> (nullable)
- `members`: Array of relation member objects with {type, ref, role} (List<Struct>, nullable)

### Tag Priority Configuration

The optimized schema uses a configurable priority system to determine which tag becomes the `category` and `type` fields when multiple top-level tags exist. The default priority order (highest to lowest):

1. **Infrastructure**: highway, railway, waterway, aeroway, power
2. **Land Use**: landuse, natural, leisure, amenity, shop, tourism
3. **Buildings**: building, man_made, historic, military
4. **Administrative**: boundary, admin_level, place
5. **Specialized**: craft, office, emergency

Priority configuration can be customized via `osm_tag_config.toml`.

## Querying Data

The optimized schema provides excellent query performance and intuitive field access:

```rust
use lancedb::{connect, Connection};
use anyhow::Result;

#[tokio::main]
async fn main() -> Result<()> {
    // Connect to the database
    let db = connect("./lancedb").execute().await?;

    // Find all restaurants using category and type fields
    let nodes_table = db.open_table("osm_nodes_compressed").execute().await?;
    let restaurants = nodes_table
        .query()
        .where_clause("category = 'amenity' AND type = 'restaurant'")
        .execute()
        .await?;

    // Find features in a bounding box using geo field [lon, lat]
    let bbox_results = nodes_table
        .query()
        .where_clause("geo[1] >= 1.0 AND geo[1] <= 2.0 AND geo[0] >= 103.0 AND geo[0] <= 104.0")
        .execute()
        .await?;

    // Find features with specific names (supports both name and name_en)
    let named_features = nodes_table
        .query()
        .where_clause("name LIKE '%park%' OR name_en LIKE '%park%'")
        .execute()
        .await?;

    println!("Found {} restaurants, {} features in bbox", restaurants.len(), bbox_results.len());
    Ok(())
}
```

### Using DataFusion SQL

```rust
use datafusion::prelude::*;
use std::sync::Arc;

// Register LanceDB table with DataFusion
let ctx = SessionContext::new();
let table = db.open_table("osm_nodes_compressed").execute().await?;
ctx.register_table("nodes", Arc::new(table.into_datafusion().await?))?;

// Run SQL queries using optimized schema
let df = ctx
    .sql("SELECT category, type, COUNT(*) as count
          FROM nodes
          WHERE category = 'amenity'
          GROUP BY category, type
          ORDER BY count DESC")
    .await?;

let results = df.collect().await?;
```

### Common Query Examples

See the [examples/queries.rs](examples/queries.rs) file for comprehensive examples including:

- Finding restaurants using category and type fields
- Spatial queries using the geo field
- Analyzing highway networks
- Searching for features with specific names
- Complex aggregation and analytics queries
- Working with extratags for detailed tag analysis

## Project Structure

```
osm-pbf-lancedb/
├── src/
│   ├── main.rs                    # CLI entry point
│   ├── lib.rs                     # Main library and driver function
│   ├── pbf.rs                     # PBF file reading and processing
│   ├── osm_arrow.rs               # OSM data types (OSMType enum)
│   ├── optimized_schema.rs        # Optimized LanceDB table schemas
│   ├── optimized_arrow.rs         # Optimized Arrow data conversion
│   ├── optimized_lancedb_sink.rs  # Optimized data writing to LanceDB
│   ├── lancedb_connection.rs      # LanceDB connection management
│   ├── tag_config.rs              # Tag priority configuration
│   └── util.rs                    # Utilities and CLI argument parsing
├── examples/
│   └── queries.rs                 # Optimized query examples and usage patterns
├── tests/
│   ├── test_lancedb.rs            # LanceDB functionality tests
│   └── test_optimized_schema.rs   # Optimized schema validation tests
├── benches/
│   └── benchmark.rs               # Performance benchmarks
└── Cargo.toml                     # Dependencies and project configuration
```

## Development

### Setup

1. [Install Rust](https://www.rust-lang.org/tools/install) (1.70 or later)
2. Clone the repository:
   ```bash
   git clone https://github.com/your-repo/osm-pbf-lancedb.git
   cd osm-pbf-lancedb
   ```
3. Build the project:
   ```bash
   cargo build --release
   ```

### Testing

Run the test suite:
```bash
cargo test
```

Test with a small PBF file:
```bash
# Download a small test file (Monaco is good for testing)
wget https://download.geofabrik.de/europe/monaco-latest.osm.pbf

# Run the tool
cargo run --release -- --input monaco-latest.osm.pbf --output ./test_lancedb
```

### Benchmarking

Run performance benchmarks:
```bash
cargo bench
```

### Getting Test Data

Download OSM PBF files from [Geofabrik](https://download.geofabrik.de/):
- Small regions (Monaco, Liechtenstein) for testing
- Larger regions (cities, countries) for performance testing
- Planet file for full-scale processing

## Performance Characteristics

osm-pbf-lancedb prioritizes import speed and query performance:

- **Multi-threaded processing**: Utilizes all available CPU cores
- **Streaming architecture**: Processes data in configurable batches to manage memory usage
- **Optimized schemas**: Type-specific tables reduce storage overhead and improve query speed
- **Automatic optimization**: Tables are optimized after import for better performance
- **Memory efficient**: Configurable batch sizes prevent memory exhaustion on large files

### Memory Usage

Memory usage scales with batch size settings:
- Default: `total_memory / cpu_count / 8` per worker thread
- Configurable via `--record-batch-target-mb` and `--file-target-mb`
- Monitor memory usage and adjust for your system

## Use Cases

This tool is ideal for:

- **GIS Analysis**: Import OSM data for spatial analysis and mapping applications
- **POI Databases**: Extract and query points of interest for location-based services
- **Route Planning**: Analyze road networks and transportation data
- **Urban Planning**: Study city infrastructure and land use patterns
- **Research**: Academic research on geographic and social patterns
- **Data Pipelines**: ETL processes for OSM data integration

## Limitations

- **Write-only**: Currently supports import only, not incremental updates
- **No spatial indexing**: Queries rely on bounding box filters rather than spatial indices
- **Memory requirements**: Large files require sufficient RAM for batch processing
- **S3 performance**: Reading from S3 is slower than local files

## Contributing

Contributions are welcome! Please:

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

Distributed under the MIT License. See `LICENSE` for more information.

## Acknowledgments

* [osmpbf](https://github.com/b-r-u/osmpbf) for PBF file reading capabilities
* [LanceDB](https://lancedb.com/) for the high-performance vector database
* [Apache Arrow](https://arrow.apache.org/) for columnar data processing
* [osm2orc](https://github.com/mojodna/osm2orc) for schema and processing inspiration
* OpenStreetMap contributors for the amazing geographic data
