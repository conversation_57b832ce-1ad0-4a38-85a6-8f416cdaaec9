use criterion::{criterion_group, criterion_main, Criterion};
use osm_pbf_lancedb::pbf_driver;
use osm_pbf_lancedb::util::Args;
use std::fs;
use std::path::Path;

async fn bench() {
    // Use Monaco PBF file (smaller file for faster benchmarking)
    let input_file = "monaco-latest.osm.pbf".to_string();

    // Ensure the input file exists
    if !Path::new(&input_file).exists() {
        panic!("Benchmark input file not found: {}. Please ensure the file exists in the project root.", input_file);
    }

    let args = Args::new(
        input_file,
        "./bench-output/".to_string(),
    );

    // Run the pbf_driver function
    pbf_driver(args).await.expect("pbf_driver should complete successfully");
}

pub fn criterion_benchmark(c: &mut Criterion) {
    // Clean up any existing benchmark output directory before starting
    let _ = fs::remove_dir_all("./bench-output/");

    c.bench_function("osm_pbf_processing", |b| {
        let rt = tokio::runtime::Builder::new_multi_thread()
            .enable_all()
            .build()
            .unwrap();
        b.to_async(rt).iter(bench)
    });

    // Clean up benchmark output directory after completion
    let _ = fs::remove_dir_all("./bench-output/");
}

criterion_group! {
    name = benches;
    config = Criterion::default()
        .sample_size(10)  // Minimum required sample size for Criterion
        .measurement_time(std::time::Duration::from_secs(30))  // Reasonable measurement time
        .warm_up_time(std::time::Duration::from_secs(5));      // Shorter warm-up time
    targets = criterion_benchmark
}
criterion_main!(benches);
