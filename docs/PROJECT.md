**Project Context:**
I want to modify this project, which currently converts OpenStreetMap PBF files to Parquet format. My goal is to adapt it to import OSM PBF files directly into LanceDB instead.

**Current Project Understanding:**
- The existing project reads OSM PBF files and converts them to Parquet format
- It handles OSM data structures (nodes, ways, relations) and their associated tags
- Uses Rust for efficient PBF parsing

**Target Requirements:**
1. Replace the Parquet output with LanceDB integration
2. Maintain the existing PBF parsing logic where possible
3. Design appropriate LanceDB table schemas for OSM data types (nodes, ways, relations)
4. Preserve all OSM tags and metadata in a queryable format
5. Ensure efficient batch writing to LanceDB for large PBF files

**Technical Considerations:**
- LanceDB uses Apache Arrow format internally
- Need to handle geospatial data efficiently (coordinates, polygons)
- Should support incremental updates for OSM changesets
- Memory efficiency is important for processing large PBF files (country/planet level)

**Expected Deliverables:**
- Modified code that writes to LanceDB instead of Parquet files
- Schema design for LanceDB tables
- Example queries for common OSM data retrieval patterns
- Performance considerations and optimization suggestions

**Additional Context:**
Use lancedb native rust lib:https://docs.rs/lancedb/latest/lancedb/index.html