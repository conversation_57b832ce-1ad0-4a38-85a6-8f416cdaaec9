# OSM Data Filtering System

This document describes the comprehensive filtering and preprocessing system for OSM data before it gets imported into LanceDB.

## Overview

The filtering system allows you to selectively import OSM data based on configurable criteria, helping to reduce database size and improve query performance by only including relevant data.

## Default Filtering Rules

By default, the system filters OSM records to only include those that meet **ALL** of the following criteria:

1. **Valid Geographic Coordinates**: Contains valid latitude and longitude values (only applies to nodes)
   - Latitude must be between -90.0 and 90.0
   - Longitude must be between -180.0 and 180.0

2. **Name Tags**: Contains at least one name tag from the following patterns:
   - `name` - Standard name tag
   - `name:*` - Multilingual name tags (e.g., `name:en`, `name:zh`, `name:fr`, etc.)

## Configuration System

### Configuration File

The filtering rules are configured via the `osm_tag_config.toml` file. Here's the filtering section:

```toml
# OSM Data Filtering Configuration
[filter]
# Whether filtering is enabled (set to false to disable all filtering)
enabled = true

# Whether to require valid geographic coordinates (latitude and longitude)
# Only applies to nodes; ways and relations don't have direct coordinates
require_coordinates = true

# Whether to require at least one name tag for inclusion
require_name_tags = true

# Patterns for acceptable name tags (supports glob-like * wildcard)
# Examples: "name" matches exactly, "name:*" matches "name:en", "name:zh", etc.
name_tag_patterns = [
    "name",
    "name:*"
]

# Custom filtering rules (for future extension)
[filter.custom_rules]
# Example: min_tag_count = "2"  # Require at least 2 tags
# Add custom rules here as needed
```

### Configuration Options

- **`enabled`**: Master switch for all filtering (default: `true`)
- **`require_coordinates`**: Validate coordinate ranges for nodes (default: `true`)
- **`require_name_tags`**: Require at least one name tag (default: `true`)
- **`name_tag_patterns`**: List of name tag patterns to accept (default: `["name", "name:*"]`)
- **`custom_rules`**: Reserved for future custom filtering rules

### Name Tag Patterns

The `name_tag_patterns` support glob-like wildcards:

- `"name"` - Matches exactly the tag `name`
- `"name:*"` - Matches any tag starting with `name:` (e.g., `name:en`, `name:zh`, `name:fr`)
- `"alt_name"` - Matches exactly the tag `alt_name`
- `"*_name"` - Matches any tag ending with `_name`

## Command Line Options

### Full Import Mode

Use the `--full` flag to bypass all filtering and import all OSM data:

```bash
osm-pbf-lancedb --input data.osm.pbf --output ./lancedb --full
```

When `--full` is used:
- All filtering rules are ignored
- Every OSM element is imported regardless of coordinates or tags
- Configuration file filtering settings are bypassed
- Useful for complete data imports or debugging

### Standard Filtered Import

```bash
osm-pbf-lancedb --input data.osm.pbf --output ./lancedb
```

This applies the filtering rules defined in `osm_tag_config.toml`.

## Filtering Statistics

The system provides detailed statistics about the filtering process:

### During Processing

```
Processed 1,234,567 elements, Accepted: 456,789 (37.0%), Filtered: 777,778 (coords: 12,345, names: 765,433, custom: 0), Database: ...
```

### Final Summary

```
Final database statistics: ...

Filtering Statistics:
  Total processed: 1,234,567
  Accepted: 456,789 (37.02%)
  Filtered out: 777,778
    - Invalid coordinates: 12,345
    - Missing name tags: 765,433
    - Custom rules: 0

Per-type filtering breakdown:
  Node: 987,654 processed, 234,567 accepted (23.75%), 753,087 filtered
  Way: 123,456 processed, 98,765 accepted (80.01%), 24,691 filtered
  Relation: 123,457 processed, 123,457 accepted (100.00%), 0 filtered
```

## Examples

### Example 1: Default Configuration

Using the default configuration will filter to include only named features:

```toml
[filter]
enabled = true
require_coordinates = true
require_name_tags = true
name_tag_patterns = ["name", "name:*"]
```

This will include:
- ✅ A node with `name=Central Park` and valid coordinates
- ✅ A way with `name:en=Main Street` 
- ✅ A relation with `name:zh=北京市`
- ❌ A node with only `highway=traffic_signals` (no name)
- ❌ A node with invalid coordinates (lat=999.0)

### Example 2: Relaxed Name Requirements

To include features with alternative name tags:

```toml
[filter]
enabled = true
require_coordinates = true
require_name_tags = true
name_tag_patterns = [
    "name",
    "name:*",
    "alt_name",
    "official_name",
    "short_name"
]
```

### Example 3: Coordinates Only

To filter only by coordinate validity (include unnamed features):

```toml
[filter]
enabled = true
require_coordinates = true
require_name_tags = false
name_tag_patterns = []
```

### Example 4: Disable All Filtering

To import everything (equivalent to `--full` flag):

```toml
[filter]
enabled = false
require_coordinates = false
require_name_tags = false
name_tag_patterns = []
```

## Performance Impact

Filtering provides several benefits:

1. **Reduced Database Size**: Excluding unnamed or invalid features can significantly reduce storage requirements
2. **Faster Queries**: Smaller datasets lead to faster query performance
3. **Improved Data Quality**: Filtering out invalid coordinates ensures data integrity

Typical filtering results:
- **Nodes**: Often 70-80% filtered out (many nodes are unnamed reference points)
- **Ways**: Usually 20-40% filtered out (most ways have names or are significant features)
- **Relations**: Typically 0-10% filtered out (relations usually represent named administrative or route features)

## Troubleshooting

### No Data After Filtering

If filtering results in very little data:

1. Check if your OSM data has name tags in the expected format
2. Consider relaxing `name_tag_patterns` to include more tag types
3. Set `require_name_tags = false` to include unnamed features
4. Use `--full` flag to import everything and analyze the data

### Configuration Not Loading

If the configuration file isn't being loaded:

1. Ensure `osm_tag_config.toml` exists in the current directory
2. Check the file syntax with a TOML validator
3. Look for error messages in the console output

### Performance Issues

If filtering is too slow:

1. Consider using `--full` for initial imports
2. Apply filtering in post-processing queries instead
3. Use more selective input data (smaller geographic regions)
