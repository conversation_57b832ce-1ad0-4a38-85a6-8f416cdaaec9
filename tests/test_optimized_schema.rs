use std::sync::Arc;
use anyhow::Result;
use lancedb::connect;
use lancedb::query::ExecutableQuery;
use tempfile::TempDir;

use osm_pbf_lancedb::tag_config::{TagConfig, process_osm_tags};
use osm_pbf_lancedb::storage_optimized_schema::{
    get_storage_optimized_schema_for_type,
};
use osm_pbf_lancedb::optimized_arrow::OptimizedOSMArrowBuilder;
use osm_pbf_lancedb::osm_arrow::OSMType;

#[test]
fn test_tag_config_priority() {
    let config = TagConfig::default();
    
    // Test that highway has higher priority than amenity
    assert!(config.get_priority("highway") > config.get_priority("amenity"));
    
    // Test that amenity has higher priority than building
    assert!(config.get_priority("amenity") > config.get_priority("building"));
    
    // Test that building has higher priority than place
    assert!(config.get_priority("building") > config.get_priority("place"));
    
    // Test default priority for unknown tags
    assert_eq!(config.get_priority("unknown_tag"), config.default_priority);
}

#[test]
fn test_tag_processing() {
    let config = TagConfig::default();
    
    let tags = vec![
        ("building", "yes"),
        ("amenity", "restaurant"),
        ("name", "Test Restaurant"),
        ("name:en", "Test Restaurant EN"),
        ("cuisine", "italian"),
        ("opening_hours", "Mo-Su 10:00-22:00"),
    ];
    
    let processed = process_osm_tags(
        12345,
        tags.iter().map(|(k, v)| (*k, *v)),
        &config,
    );
    
    // Verify basic fields
    assert_eq!(processed.osm_id, 12345);
    assert_eq!(processed.name, Some("Test Restaurant".to_string()));
    assert_eq!(processed.name_en, Some("Test Restaurant EN".to_string()));
    
    // Verify category/type extraction (amenity should win over building)
    assert_eq!(processed.category, Some("amenity".to_string()));
    assert_eq!(processed.tag_type, Some("restaurant".to_string()));
    
    // Verify extratags (should include building, cuisine, opening_hours but not name, name:en, amenity)
    assert!(processed.extratags.contains_key("building"));
    assert!(processed.extratags.contains_key("cuisine"));
    assert!(processed.extratags.contains_key("opening_hours"));
    assert!(!processed.extratags.contains_key("name"));
    assert!(!processed.extratags.contains_key("name:en"));
    assert!(!processed.extratags.contains_key("amenity"));
}

#[test]
fn test_storage_optimized_schemas() {
    // Test that all storage-optimized schemas can be created
    let node_schema = get_storage_optimized_schema_for_type(&OSMType::Node);
    let way_schema = get_storage_optimized_schema_for_type(&OSMType::Way);
    let relation_schema = get_storage_optimized_schema_for_type(&OSMType::Relation);

    // Verify field counts
    assert_eq!(node_schema.fields().len(), 7);
    assert_eq!(way_schema.fields().len(), 7);
    assert_eq!(relation_schema.fields().len(), 7);

    // Verify required fields exist
    assert!(node_schema.field_with_name("osm_id").is_ok());
    assert!(node_schema.field_with_name("geo").is_ok());
    assert!(way_schema.field_with_name("nds").is_ok());
    assert!(relation_schema.field_with_name("members").is_ok());

    // Verify storage-optimized data types
    let osm_id_field = node_schema.field_with_name("osm_id").unwrap();
    assert_eq!(osm_id_field.data_type(), &arrow::datatypes::DataType::UInt64);

    let geo_field = node_schema.field_with_name("geo").unwrap();
    match geo_field.data_type() {
        arrow::datatypes::DataType::FixedSizeList(inner_field, size) => {
            assert_eq!(*size, 2);
            assert_eq!(inner_field.data_type(), &arrow::datatypes::DataType::Float32);
        }
        _ => panic!("geo field should be FixedSizeList with Float32"),
    }
}

#[test]
fn test_optimized_arrow_builder() {
    let config = TagConfig::default();
    let mut builder = OptimizedOSMArrowBuilder::new(config);
    
    // Test building a node record
    let tags = vec![
        ("amenity".to_string(), "restaurant".to_string()),
        ("name".to_string(), "Test Restaurant".to_string()),
        ("cuisine".to_string(), "italian".to_string()),
    ];
    
    let _size = builder.append_row(
        12345,
        OSMType::Node,
        tags.into_iter(),
        Some(1.234),
        Some(103.567),
        std::iter::empty(),
        std::iter::empty(),
    );
    
    // Finish and create record batch
    let batch = builder.finish(&OSMType::Node).unwrap();
    
    // Verify batch structure
    assert_eq!(batch.num_rows(), 1);
    assert_eq!(batch.num_columns(), 7);
    
    // Verify schema matches expected storage-optimized schema
    let expected_schema = get_storage_optimized_schema_for_type(&OSMType::Node);
    assert_eq!(batch.schema().fields().len(), expected_schema.fields().len());
}

#[test]
fn test_way_processing() {
    let config = TagConfig::default();
    let mut builder = OptimizedOSMArrowBuilder::new(config);
    
    // Test building a way record
    let tags = vec![
        ("highway".to_string(), "primary".to_string()),
        ("name".to_string(), "Main Street".to_string()),
        ("maxspeed".to_string(), "50".to_string()),
    ];
    
    let node_refs = vec![1001, 1002, 1003, 1004];
    
    let _size = builder.append_row(
        54321,
        OSMType::Way,
        tags.into_iter(),
        None, // Ways don't have direct coordinates
        None,
        node_refs.into_iter(),
        std::iter::empty(),
    );
    
    // Finish and create record batch
    let batch = builder.finish(&OSMType::Way).unwrap();
    
    // Verify batch structure
    assert_eq!(batch.num_rows(), 1);
    assert_eq!(batch.num_columns(), 7);
    
    // Verify schema matches expected storage-optimized schema
    let expected_schema = get_storage_optimized_schema_for_type(&OSMType::Way);
    assert_eq!(batch.schema().fields().len(), expected_schema.fields().len());
}

#[test]
fn test_relation_processing() {
    let config = TagConfig::default();
    let mut builder = OptimizedOSMArrowBuilder::new(config);
    
    // Test building a relation record
    let tags = vec![
        ("type".to_string(), "route".to_string()),
        ("route".to_string(), "bus".to_string()),
        ("name".to_string(), "Bus Route 123".to_string()),
    ];
    
    let members = vec![
        ("way".to_string(), 2001, Some("".to_string())),
        ("way".to_string(), 2002, Some("".to_string())),
        ("node".to_string(), 3001, Some("stop".to_string())),
    ];
    
    let _size = builder.append_row(
        98765,
        OSMType::Relation,
        tags.into_iter(),
        None, // Relations don't have direct coordinates
        None,
        std::iter::empty(),
        members.into_iter(),
    );
    
    // Finish and create record batch
    let batch = builder.finish(&OSMType::Relation).unwrap();
    
    // Verify batch structure
    assert_eq!(batch.num_rows(), 1);
    assert_eq!(batch.num_columns(), 7);
    
    // Verify schema matches expected storage-optimized schema
    let expected_schema = get_storage_optimized_schema_for_type(&OSMType::Relation);
    assert_eq!(batch.schema().fields().len(), expected_schema.fields().len());
}

#[test]
fn test_tag_priority_edge_cases() {
    let config = TagConfig::default();
    
    // Test with no tags
    let processed = process_osm_tags(
        123,
        std::iter::empty(),
        &config,
    );
    assert_eq!(processed.category, None);
    assert_eq!(processed.tag_type, None);
    assert_eq!(processed.name, None);
    assert!(processed.extratags.is_empty());
    
    // Test with only excluded tags (but name should still be extracted)
    let tags = vec![("name", "Test"), ("changeset", "12345")];
    let processed = process_osm_tags(
        123,
        tags.iter().map(|(k, v)| (*k, *v)),
        &config,
    );
    assert_eq!(processed.category, None);
    assert_eq!(processed.tag_type, None);
    assert_eq!(processed.name, Some("Test".to_string())); // name should still be extracted
    assert!(processed.extratags.is_empty()); // but excluded from extratags
    
    // Test with single tag
    let tags = vec![("highway", "primary")];
    let processed = process_osm_tags(
        123,
        tags.iter().map(|(k, v)| (*k, *v)),
        &config,
    );
    assert_eq!(processed.category, Some("highway".to_string()));
    assert_eq!(processed.tag_type, Some("primary".to_string()));
    assert!(processed.extratags.is_empty());
}

#[tokio::test]
async fn test_optimized_schema_with_lancedb() -> Result<()> {
    // Create temporary directory for test database
    let temp_dir = TempDir::new()?;
    let db_path = temp_dir.path().join("test_optimized_db");
    
    // Connect to LanceDB
    let db = connect(db_path.to_str().unwrap()).execute().await?;
    
    // Create storage-optimized node table
    let schema = Arc::new(get_storage_optimized_schema_for_type(&OSMType::Node));
    let table = db.create_empty_table("test_nodes_storage_optimized", schema).execute().await?;
    
    // Create test data
    let config = TagConfig::default();
    let mut builder = OptimizedOSMArrowBuilder::new(config);
    
    // Add test records
    let test_data = vec![
        (1, "amenity", "restaurant", "Test Restaurant", Some(1.234), Some(103.567)),
        (2, "shop", "supermarket", "Test Market", Some(1.235), Some(103.568)),
        (3, "highway", "traffic_signals", "", Some(1.236), Some(103.569)),
    ];
    
    for (id, category, tag_type, name, lat, lon) in test_data {
        let mut tags = vec![
            (category.to_string(), tag_type.to_string()),
        ];
        if !name.is_empty() {
            tags.push(("name".to_string(), name.to_string()));
        }
        
        builder.append_row(
            id,
            OSMType::Node,
            tags.into_iter(),
            lat,
            lon,
            std::iter::empty(),
            std::iter::empty(),
        );
    }
    
    let batch = builder.finish(&OSMType::Node)?;

    // Add data to table
    use arrow::record_batch::RecordBatchIterator;
    let schema = batch.schema();
    let batch_iter = RecordBatchIterator::new(
        vec![Ok(batch)].into_iter(),
        schema,
    );
    table.add(Box::new(batch_iter)).execute().await?;
    
    // Query the data back
    let mut stream = table.query().execute().await?;

    // Collect results from stream
    use futures::stream::StreamExt;
    let mut batches = Vec::new();
    while let Some(batch_result) = stream.next().await {
        batches.push(batch_result?);
    }

    // Verify we got data back
    assert!(!batches.is_empty());
    let first_batch = &batches[0];
    assert_eq!(first_batch.num_rows(), 3);
    
    println!("Successfully tested optimized schema with LanceDB");
    Ok(())
}

#[test]
fn test_config_serialization() {
    let config = TagConfig::default();
    
    // Test TOML serialization
    let toml_str = toml::to_string(&config).unwrap();
    assert!(toml_str.contains("tag_priorities"));
    assert!(toml_str.contains("excluded_tags"));
    
    // Test deserialization
    let deserialized: TagConfig = toml::from_str(&toml_str).unwrap();
    assert_eq!(config.get_priority("highway"), deserialized.get_priority("highway"));
    assert_eq!(config.excluded_tags, deserialized.excluded_tags);
}
