use std::sync::Arc;
use anyhow::Result;
use lancedb::connect;
use arrow::record_batch::RecordBatch;
use arrow::array::{Int64Array, StringArray};
use arrow::datatypes::{DataType, Field, Schema};

#[tokio::test]
async fn test_basic_lancedb_functionality() -> Result<()> {
    println!("Testing basic LanceDB functionality...");
    
    // Connect to LanceDB
    let db = connect("lancedb/test_simple")
        .execute()
        .await?;
    
    println!("Connected to LanceDB successfully");
    
    // Create a simple schema
    let schema = Arc::new(Schema::new(vec![
        Field::new("id", DataType::Int64, false),
        Field::new("name", DataType::Utf8, false),
    ]));
    
    // Create a simple record batch
    let id_array = Int64Array::from(vec![1, 2, 3]);
    let name_array = StringArray::from(vec!["<PERSON>", "<PERSON>", "<PERSON>"]);
    
    let batch = RecordBatch::try_new(
        schema.clone(),
        vec![Arc::new(id_array), Arc::new(name_array)],
    )?;
    
    println!("Created test record batch with {} rows", batch.num_rows());
    
    // Create table
    let batch_iter = arrow::record_batch::RecordBatchIterator::new(
        vec![Ok(batch)].into_iter(),
        schema,
    );
    
    println!("Creating table...");

    // Drop table if it exists
    if let Ok(_) = db.open_table("test_table").execute().await {
        db.drop_table("test_table").await?;
    }

    let _table = db
        .create_table("test_table", Box::new(batch_iter))
        .execute()
        .await?;
    
    println!("Table created successfully!");
    
    Ok(())
}
