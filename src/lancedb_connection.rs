use std::sync::Arc;
use anyhow::{Context, Result};
use lancedb::{connect, Connection, Table};
use lancedb::table::OptimizeAction;
use arrow::record_batch::{RecordBatch, RecordBatchIterator};

use log::{info, warn};

use crate::storage_optimized_schema::{
    get_storage_optimized_schema_for_type,
    get_storage_optimized_table_name,
    get_storage_optimized_indices,
    CompressionConfig
};
use crate::osm_arrow::OSMType;

/// LanceDB connection manager for OSM data
pub struct LanceDBManager {
    db: Connection,
}

impl LanceDBManager {
    /// Create a new LanceDB manager with a connection to the specified database path
    pub async fn new(db_path: &str) -> Result<Self> {
        info!("Connecting to LanceDB at: {}", db_path);

        let db = connect(db_path)
            .execute()
            .await
            .with_context(|| format!("Failed to connect to LanceDB at {}", db_path))?;

        info!("Successfully connected to LanceDB");

        Ok(LanceDBManager { db })
    }

    /// Initialize all required tables for OSM data types (deprecated - use initialize_optimized_tables)
    pub async fn initialize_tables(&self) -> Result<()> {
        // This function has been deprecated in favor of initialize_optimized_tables
        // Redirect to the optimized version
        self.initialize_optimized_tables().await
    }

    /// Initialize optimized tables for OSM data types
    pub async fn initialize_optimized_tables(&self) -> Result<()> {
        info!("Initializing storage-optimized OSM tables in LanceDB");

        // Create optimized tables for each OSM type
        for osm_type in [OSMType::Node, OSMType::Way, OSMType::Relation] {
            self.create_optimized_table_if_not_exists(&osm_type).await?;
        }

        info!("All storage-optimized OSM tables initialized successfully");
        Ok(())
    }

    /// Add data to optimized table with compression
    pub async fn add_optimized_data_compressed(
        &self,
        osm_type: &OSMType,
        batches: Vec<RecordBatch>,
    ) -> Result<()> {
        let table = self.get_optimized_table(osm_type).await?;
        let compression_config = CompressionConfig::for_osm_type(osm_type);

        info!(
            "Writing {} batches with {} compression (level {}) for {:?}",
            batches.len(),
            compression_config.algorithm,
            compression_config.level,
            osm_type
        );

        for batch in batches {
            let schema = batch.schema();
            let batch_iter = RecordBatchIterator::new(
                vec![Ok(batch)].into_iter(),
                schema,
            );

            // Note: LanceDB compression settings may vary by version
            // This is the intended API based on the optimization guide
            table.add(Box::new(batch_iter))
                .execute()
                .await
                .with_context(|| format!("Failed to write compressed batch for {:?}", osm_type))?;
        }

        Ok(())
    }



    /// Create an optimized table for the specified OSM type if it doesn't exist
    async fn create_optimized_table_if_not_exists(&self, osm_type: &OSMType) -> Result<()> {
        let table_name = get_storage_optimized_table_name(osm_type);

        // Check if table already exists
        let table_names = self.db.table_names().execute().await?;
        if table_names.contains(&table_name.to_string()) {
            info!("Storage-optimized table '{}' already exists, skipping creation", table_name);
            return Ok(());
        }

        info!("Creating storage-optimized table: {}", table_name);

        // Try to create an empty table first using create_empty_table
        let schema = Arc::new(get_storage_optimized_schema_for_type(osm_type));

        match self.db
            .create_empty_table(table_name, schema.clone())
            .execute()
            .await
        {
            Ok(_) => {
                // Success with empty table creation
                info!("Created empty storage-optimized table: {}", table_name);

                // Create recommended indices for better query performance
                self.create_optimized_indices(table_name, osm_type).await?;
            }
            Err(e) => {
                // If empty table creation fails, try with a minimal batch
                info!("Empty storage-optimized table creation failed ({}), trying with minimal batch", e);
                let dummy_batch = self.create_minimal_optimized_batch(osm_type)?;
                let batch_iter = RecordBatchIterator::new(
                    vec![Ok(dummy_batch)].into_iter(),
                    schema,
                );

                self.db
                    .create_table(table_name, Box::new(batch_iter))
                    .execute()
                    .await
                    .with_context(|| format!("Failed to create storage-optimized table {} with minimal batch", table_name))?;

                // Create recommended indices for better query performance
                self.create_optimized_indices(table_name, osm_type).await?;
            }
        }

        info!("Successfully created storage-optimized table: {}", table_name);
        Ok(())
    }





    /// Get database statistics for storage-optimized tables
    pub async fn get_stats(&self) -> Result<DatabaseStats> {
        let mut stats = DatabaseStats::default();

        for osm_type in [OSMType::Node, OSMType::Way, OSMType::Relation] {
            let table_name = get_storage_optimized_table_name(&osm_type);

            // Check if table exists
            let table_names = self.db.table_names().execute().await?;
            if !table_names.contains(&table_name.to_string()) {
                continue;
            }

            match self.get_optimized_table(&osm_type).await {
                Ok(table) => {
                    match table.count_rows(None).await {
                        Ok(count) => {
                            match osm_type {
                                OSMType::Node => stats.node_count = count,
                                OSMType::Way => stats.way_count = count,
                                OSMType::Relation => stats.relation_count = count,
                            }
                        }
                        Err(e) => {
                            warn!("Failed to count rows in table {}: {}", table_name, e);
                        }
                    }
                }
                Err(e) => {
                    warn!("Failed to open table {}: {}", table_name, e);
                }
            }
        }

        Ok(stats)
    }

    /// Optimize storage-optimized tables (compact, create indices, etc.)
    pub async fn optimize_tables(&self) -> Result<()> {
        info!("Optimizing storage-optimized LanceDB tables");

        for osm_type in [OSMType::Node, OSMType::Way, OSMType::Relation] {
            let table_name = get_storage_optimized_table_name(&osm_type);

            match self.get_optimized_table(&osm_type).await {
                Ok(table) => {
                    // Compact the table to optimize storage
                    if let Err(e) = table.optimize(OptimizeAction::All).await {
                        warn!("Failed to optimize table {}: {}", table_name, e);
                    } else {
                        info!("Optimized table: {}", table_name);
                    }
                }
                Err(e) => {
                    warn!("Failed to open table {} for optimization: {}", table_name, e);
                }
            }
        }

        info!("Storage-optimized table optimization completed");
        Ok(())
    }

    /// Create a minimal record batch using OptimizedOSMArrowBuilder for table initialization
    fn create_minimal_optimized_batch(&self, osm_type: &OSMType) -> Result<RecordBatch> {
        use crate::optimized_arrow::OptimizedOSMArrowBuilder;
        use crate::tag_config::TagConfig;

        let tag_config = TagConfig::default();
        let mut builder = OptimizedOSMArrowBuilder::new(tag_config);

        // Add one minimal record of the appropriate type
        match osm_type {
            OSMType::Node => {
                let _size = builder.append_row(
                    -1i64, // dummy id (negative to avoid conflicts)
                    OSMType::Node,
                    std::iter::empty(), // no tags
                    Some(0.0), // lat
                    Some(0.0), // lon
                    std::iter::empty(), // no nodes
                    std::iter::empty(), // no members
                );
            }
            OSMType::Way => {
                let _size = builder.append_row(
                    -1i64, // dummy id (negative to avoid conflicts)
                    OSMType::Way,
                    std::iter::empty(), // no tags
                    None, // no lat
                    None, // no lon
                    std::iter::empty(), // no nodes
                    std::iter::empty(), // no members
                );
            }
            OSMType::Relation => {
                let _size = builder.append_row(
                    -1i64, // dummy id (negative to avoid conflicts)
                    OSMType::Relation,
                    std::iter::empty(), // no tags
                    None, // no lat
                    None, // no lon
                    std::iter::empty(), // no nodes
                    std::iter::empty(), // no members
                );
            }
        }

        builder.finish(osm_type)
            .with_context(|| format!("Failed to create minimal optimized batch for {:?}", osm_type))
    }

    /// Create recommended indices for storage-optimized tables
    async fn create_optimized_indices(&self, table_name: &str, osm_type: &OSMType) -> Result<()> {
        let indices = get_storage_optimized_indices(osm_type);

        for index_field in indices {
            info!("Creating index on {}.{}", table_name, index_field);

            // Note: LanceDB index creation syntax may vary
            // This is a placeholder for the actual index creation logic
            // You may need to adjust based on LanceDB's current API
            match self.db.open_table(table_name).execute().await {
                Ok(_table) => {
                    // Create index if supported by LanceDB version
                    // table.create_index(&index_field).await?;
                    info!("Index creation for {} would be implemented here", index_field);
                }
                Err(e) => {
                    warn!("Failed to open table {} for index creation: {}", table_name, e);
                }
            }
        }

        Ok(())
    }

    /// Get a storage-optimized table for the specified OSM type
    pub async fn get_optimized_table(&self, osm_type: &OSMType) -> Result<Table> {
        let table_name = get_storage_optimized_table_name(osm_type);
        self.db.open_table(table_name)
            .execute()
            .await
            .with_context(|| format!("Failed to open storage-optimized table: {}", table_name))
    }

    /// Create a storage-optimized table for the specified OSM type if it doesn't exist (public method)
    pub async fn ensure_optimized_table_exists(&self, osm_type: &OSMType) -> Result<()> {
        self.create_optimized_table_if_not_exists(osm_type).await
    }
}

/// Database statistics
#[derive(Debug, Default)]
pub struct DatabaseStats {
    pub node_count: usize,
    pub way_count: usize,
    pub relation_count: usize,
}

impl DatabaseStats {
    pub fn total_count(&self) -> usize {
        self.node_count + self.way_count + self.relation_count
    }
}

impl std::fmt::Display for DatabaseStats {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "Nodes: {}, Ways: {}, Relations: {}, Total: {}",
            self.node_count,
            self.way_count,
            self.relation_count,
            self.total_count()
        )
    }
}
