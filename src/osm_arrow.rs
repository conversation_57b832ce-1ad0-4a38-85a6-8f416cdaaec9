use std::fmt;

// This file has been simplified to only contain the OSMType enum.
// All legacy Arrow builder implementations have been removed in favor
// of the optimized implementations in optimized_arrow.rs.

#[derive(Clone, Debug, Eq, PartialEq, Hash)]
pub enum OSMType {
    Node,
    Way,
    Relation,
}

impl fmt::Display for OSMType {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        write!(formatter, "{}", format!("{:?}", self).to_lowercase())
    }
}



