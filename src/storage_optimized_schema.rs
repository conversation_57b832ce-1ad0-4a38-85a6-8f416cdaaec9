// Storage-optimized schema implementation
// This provides significant storage savings while maintaining query performance

use std::sync::Arc;
use arrow::datatypes::{DataType, Field, Fields, Schema};
use crate::osm_arrow::OSMType;

/// Storage-optimized table names
pub const OPTIMIZED_NODE_TABLE_NAME: &str = "osm_nodes_compressed";
pub const OPTIMIZED_WAY_TABLE_NAME: &str = "osm_ways_compressed";
pub const OPTIMIZED_RELATION_TABLE_NAME: &str = "osm_relations_compressed";

/// Get storage-optimized table name for OSM type
pub fn get_storage_optimized_table_name(osm_type: &OSMType) -> &'static str {
    match osm_type {
        OSMType::Node => OPTIMIZED_NODE_TABLE_NAME,
        OSMType::Way => OPTIMIZED_WAY_TABLE_NAME,
        OSMType::Relation => OPTIMIZED_RELATION_TABLE_NAME,
    }
}

/// Create storage-optimized schema with smaller data types and compression-friendly structure
pub fn get_storage_optimized_schema_for_type(osm_type: &OSMType) -> Schema {
    let mut fields = vec![
        // Use UInt64 for OSM IDs (always positive)
        Field::new("osm_id", DataType::UInt64, false),
        
        // Use dictionary encoding for names (many duplicates)
        Field::new("name", DataType::Dictionary(
            Box::new(DataType::UInt32),
            Box::new(DataType::Utf8)
        ), true),
        
        Field::new("name_en", DataType::Dictionary(
            Box::new(DataType::UInt32),
            Box::new(DataType::Utf8)
        ), true),
        
        // Use dictionary encoding for categories (limited set)
        Field::new("category", DataType::Dictionary(
            Box::new(DataType::UInt16),
            Box::new(DataType::Utf8)
        ), true),
        
        Field::new("type", DataType::Dictionary(
            Box::new(DataType::UInt16),
            Box::new(DataType::Utf8)
        ), true),
        
        // Optimize extratags structure
        Field::new("extratags", DataType::List(Arc::new(Field::new(
            "item",
            DataType::Struct(Fields::from(vec![
                Field::new("key", DataType::Dictionary(
                    Box::new(DataType::UInt16),
                    Box::new(DataType::Utf8)
                ), false),
                Field::new("value", DataType::Dictionary(
                    Box::new(DataType::UInt16),
                    Box::new(DataType::Utf8)
                ), false),
            ])),
            false,
        ))), true),
    ];
    
    // Add type-specific optimized fields
    match osm_type {
        OSMType::Node => {
            // Use Float32 for coordinates (sufficient precision for OSM)
            fields.push(Field::new("geo", DataType::FixedSizeList(
                Arc::new(Field::new("item", DataType::Float32, false)), 2
            ), false));
        }
        OSMType::Way => {
            // Use UInt64 for node references
            fields.push(Field::new("nds", 
                DataType::List(Arc::new(Field::new("item", DataType::UInt64, false))), 
                true
            ));
        }
        OSMType::Relation => {
            // Optimize member structure with enum for type
            fields.push(Field::new("members", DataType::List(Arc::new(Field::new(
                "item",
                DataType::Struct(Fields::from(vec![
                    // Use UInt8 enum: 0=node, 1=way, 2=relation
                    Field::new("type", DataType::UInt8, false),
                    Field::new("ref", DataType::UInt64, false),
                    Field::new("role", DataType::Dictionary(
                        Box::new(DataType::UInt16),
                        Box::new(DataType::Utf8)
                    ), true),
                ])),
                false,
            ))), true));
        }
    }
    
    Schema::new(fields)
}

/// Compression settings for different data types
pub struct CompressionConfig {
    pub algorithm: &'static str,
    pub level: i32,
    pub batch_size_mb: usize,
}

impl CompressionConfig {
    /// Get optimal compression settings for OSM type
    pub fn for_osm_type(osm_type: &OSMType) -> Self {
        match osm_type {
            OSMType::Node => CompressionConfig {
                algorithm: "zstd",  // Best compression for repetitive coordinate data
                level: 3,           // Balance speed vs compression
                batch_size_mb: 32,  // Larger batches for better compression
            },
            OSMType::Way => CompressionConfig {
                algorithm: "lz4",   // Faster compression for variable-length data
                level: 1,           // Prioritize speed
                batch_size_mb: 64,  // Large batches for way node lists
            },
            OSMType::Relation => CompressionConfig {
                algorithm: "zstd",  // Good compression for structured data
                level: 2,           // Moderate compression
                batch_size_mb: 16,  // Smaller batches for complex structures
            },
        }
    }
}

/// Storage efficiency metrics
pub struct StorageMetrics {
    pub uncompressed_bytes: u64,
    pub compressed_bytes: u64,
    pub compression_ratio: f64,
    pub records_per_mb: u64,
}

impl StorageMetrics {
    pub fn new(uncompressed: u64, compressed: u64, record_count: u64) -> Self {
        let compression_ratio = if uncompressed > 0 {
            compressed as f64 / uncompressed as f64
        } else {
            1.0
        };
        
        let records_per_mb = if compressed > 0 {
            (record_count * 1024 * 1024) / compressed
        } else {
            0
        };
        
        StorageMetrics {
            uncompressed_bytes: uncompressed,
            compressed_bytes: compressed,
            compression_ratio,
            records_per_mb,
        }
    }
    
    pub fn compression_percentage(&self) -> f64 {
        (1.0 - self.compression_ratio) * 100.0
    }
}

/// Recommended indices for storage-optimized schema
pub fn get_storage_optimized_indices(osm_type: &OSMType) -> Vec<&'static str> {
    let mut indices = vec![
        "category",  // High selectivity for filtering
        "type",      // Often used with category
    ];
    
    match osm_type {
        OSMType::Node => {
            indices.push("geo");  // Spatial index for geographic queries
        }
        OSMType::Way => {
            // Ways typically queried by category/type, not geometry
        }
        OSMType::Relation => {
            // Relations typically queried by category/type
        }
    }
    
    indices
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_storage_optimized_schema() {
        let node_schema = get_storage_optimized_schema_for_type(&OSMType::Node);
        assert_eq!(node_schema.fields().len(), 7); // osm_id, name, name_en, category, type, extratags, geo
        
        let way_schema = get_storage_optimized_schema_for_type(&OSMType::Way);
        assert_eq!(way_schema.fields().len(), 7); // osm_id, name, name_en, category, type, extratags, nds
        
        let relation_schema = get_storage_optimized_schema_for_type(&OSMType::Relation);
        assert_eq!(relation_schema.fields().len(), 7); // osm_id, name, name_en, category, type, extratags, members
    }
    
    #[test]
    fn test_compression_config() {
        let node_config = CompressionConfig::for_osm_type(&OSMType::Node);
        assert_eq!(node_config.algorithm, "zstd");
        assert_eq!(node_config.batch_size_mb, 32);
        
        let way_config = CompressionConfig::for_osm_type(&OSMType::Way);
        assert_eq!(way_config.algorithm, "lz4");
        assert_eq!(way_config.batch_size_mb, 64);
    }
    
    #[test]
    fn test_storage_metrics() {
        let metrics = StorageMetrics::new(1000, 300, 100);
        assert_eq!(metrics.compression_ratio, 0.3);
        assert_eq!(metrics.compression_percentage(), 70.0);
    }
}
