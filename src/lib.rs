use std::sync::Arc;
use anyhow::Result;
use url::Url;

pub mod osm_arrow; // Only contains OSMType enum now
pub mod pbf;
pub mod util;
pub mod lancedb_connection;
pub mod tag_config;
pub mod storage_optimized_schema; // Storage-optimized schema with compression support
pub mod optimized_arrow;
pub mod optimized_lancedb_sink;

use crate::pbf::{
    create_local_buf_reader, create_s3_buf_reader, process_blobs_lancedb, finish_lancedb_sinks,
};
use crate::lancedb_connection::LanceDBManager;
use crate::optimized_lancedb_sink::{create_optimized_sinkpools, OptimizedLanceDBSinkpoolStore, collect_filter_stats};
use crate::tag_config::TagConfig;
use crate::util::{Args, ARGS};

pub async fn pbf_driver(args: Args) -> Result<(), anyhow::Error> {
    // Store value for reading across threads (write-once)
    let _ = ARGS.set(args.clone());

    // Initialize LanceDB connection
    let db_manager = Arc::new(LanceDBManager::new(&args.output).await?);
    db_manager.initialize_optimized_tables().await?;

    // Load tag configuration (try from file first, fall back to default)
    let tag_config = if std::path::Path::new("osm_tag_config.toml").exists() {
        match TagConfig::from_file("osm_tag_config.toml") {
            Ok(config) => {
                println!("Loaded configuration from osm_tag_config.toml");
                if args.full {
                    println!("⚠️  --full flag enabled: All filtering rules will be bypassed");
                } else if config.filter.enabled {
                    println!("✅ Filtering enabled with the following rules:");
                    println!("   - Require coordinates: {}", config.filter.require_coordinates);
                    println!("   - Require name tags: {}", config.filter.require_name_tags);
                    println!("   - Name tag patterns: {:?}", config.filter.name_tag_patterns);
                } else {
                    println!("⚠️  Filtering disabled in configuration");
                }
                config
            }
            Err(e) => {
                println!("⚠️  Failed to load osm_tag_config.toml: {}", e);
                println!("Using default configuration");
                TagConfig::default()
            }
        }
    } else {
        println!("No osm_tag_config.toml found, using default configuration");
        if args.full {
            println!("⚠️  --full flag enabled: All filtering rules will be bypassed");
        } else {
            println!("✅ Using default filtering rules (require coordinates and name tags)");
        }
        TagConfig::default()
    };

    // Create optimized sinkpools
    let sinkpools: Arc<OptimizedLanceDBSinkpoolStore> = Arc::new(
        create_optimized_sinkpools(db_manager.clone(), tag_config.clone())?
    );

    let full_path = args.input;
    let buf_reader = if let Ok(url) = Url::parse(&full_path) {
        create_s3_buf_reader(url).await?
    } else {
        create_local_buf_reader(&full_path).await?
    };

    // Process the PBF file using optimized sinks
    process_blobs_lancedb(buf_reader, sinkpools.clone(), db_manager.clone(), tag_config).await?;

    finish_lancedb_sinks(sinkpools.clone(), true).await?;

    // Optimize tables after all data is written
    db_manager.optimize_tables().await?;

    // Print final statistics including filtering information
    let db_stats = db_manager.get_stats().await?;
    let filter_stats = collect_filter_stats(sinkpools.clone());

    println!("Final database statistics: {}", db_stats);

    // Print filtering statistics if filtering was applied
    let mut total_filter_stats = crate::tag_config::FilterStats::new();
    for (_, stats) in &filter_stats {
        total_filter_stats.total_processed += stats.total_processed;
        total_filter_stats.filtered_out += stats.filtered_out;
        total_filter_stats.filtered_no_coordinates += stats.filtered_no_coordinates;
        total_filter_stats.filtered_no_name_tags += stats.filtered_no_name_tags;
        total_filter_stats.filtered_custom_rules += stats.filtered_custom_rules;
    }

    // Print filtering statistics based on whether --full flag was used
    // Debug: Print the actual value of args.full
    eprintln!("DEBUG: args.full = {}", args.full);
    if args.full {
        println!("\nNo filtering was applied (--full flag used)");
    } else if total_filter_stats.total_processed > 0 {
        println!("\nFiltering Statistics:");
        println!("  Total processed: {}", total_filter_stats.total_processed);
        println!("  Accepted: {} ({:.2}%)", total_filter_stats.accepted(), total_filter_stats.acceptance_rate() * 100.0);
        println!("  Filtered out: {}", total_filter_stats.filtered_out);
        if total_filter_stats.filtered_no_coordinates > 0 {
            println!("    - Invalid coordinates: {}", total_filter_stats.filtered_no_coordinates);
        }
        if total_filter_stats.filtered_no_name_tags > 0 {
            println!("    - Missing name tags: {}", total_filter_stats.filtered_no_name_tags);
        }
        if total_filter_stats.filtered_custom_rules > 0 {
            println!("    - Custom rules: {}", total_filter_stats.filtered_custom_rules);
        }

        // Print per-type breakdown
        println!("\nPer-type filtering breakdown:");
        for (osm_type, stats) in filter_stats {
            if stats.total_processed > 0 {
                println!("  {:?}: {} processed, {} accepted ({:.2}%), {} filtered",
                    osm_type,
                    stats.total_processed,
                    stats.accepted(),
                    stats.acceptance_rate() * 100.0,
                    stats.filtered_out
                );
            }
        }
    } else {
        // Filtering was enabled but no elements were processed
        // This could happen with empty input files or configuration issues
        println!("\nFiltering was enabled but no elements were processed");
    }

    Ok(())
}
