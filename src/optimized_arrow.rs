use std::sync::Arc;
use arrow::array::builder::{
    FixedSizeListBuilder, Float32Builder, UInt64Builder, ListBuilder,
    StructBuilder, StringDictionaryBuilder,
};
use arrow::array::ArrayRef;
use arrow::datatypes::{DataType, Field, Fields, UInt16Type, UInt32Type};
use arrow::error::ArrowError;
use arrow::record_batch::RecordBatch;
use once_cell::sync::Lazy;
use dashmap::DashMap;

use crate::osm_arrow::OSMType;
use crate::storage_optimized_schema::get_storage_optimized_schema_for_type;
use crate::tag_config::{TagConfig, process_osm_tags};

// String interning for common values (Strategy 4: String Optimization)
static CATEGORY_INTERN: Lazy<DashMap<String, Arc<str>>> = Lazy::new(DashMap::new);
static TYPE_INTERN: Lazy<DashMap<String, Arc<str>>> = Lazy::new(DashMap::new);

/// Storage-optimized Arrow builder for OSM data using compression-friendly schema
pub struct OptimizedOSMArrowBuilder {
    osm_type: Option<OSMType>,
    tag_config: TagConfig,

    // Common fields for all types (using storage-optimized types with dictionary encoding)
    osm_id_builder: Box<UInt64Builder>,  // UInt64 for positive IDs
    name_builder: Box<StringDictionaryBuilder<UInt32Type>>,  // Dictionary encoding for names
    name_en_builder: Box<StringDictionaryBuilder<UInt32Type>>,  // Dictionary encoding for names
    category_builder: Box<StringDictionaryBuilder<UInt16Type>>,  // Dictionary encoding for categories
    type_builder: Box<StringDictionaryBuilder<UInt16Type>>,  // Dictionary encoding for types
    extratags_builder: Box<ListBuilder<StructBuilder>>,

    // Node-specific fields
    geo_builder: Option<Box<FixedSizeListBuilder<Float32Builder>>>,  // Float32 for coordinates

    // Way-specific fields
    nds_builder: Option<Box<ListBuilder<UInt64Builder>>>,  // UInt64 for node references

    // Relation-specific fields
    members_builder: Option<Box<ListBuilder<StructBuilder>>>,
}

impl OptimizedOSMArrowBuilder {
    /// Create a new storage-optimized builder with dictionary encoding
    pub fn new(tag_config: TagConfig) -> Self {
        let osm_id_builder = Box::new(UInt64Builder::new());
        let name_builder = Box::new(StringDictionaryBuilder::<UInt32Type>::new());
        let name_en_builder = Box::new(StringDictionaryBuilder::<UInt32Type>::new());
        let category_builder = Box::new(StringDictionaryBuilder::<UInt16Type>::new());
        let type_builder = Box::new(StringDictionaryBuilder::<UInt16Type>::new());

        // Create extratags builder (List<Struct>) with dictionary encoding
        let extratags_struct_builder = StructBuilder::new(
            vec![
                Field::new("key", DataType::Dictionary(
                    Box::new(DataType::UInt16),
                    Box::new(DataType::Utf8)
                ), false),
                Field::new("value", DataType::Dictionary(
                    Box::new(DataType::UInt16),
                    Box::new(DataType::Utf8)
                ), false),
            ],
            vec![
                Box::new(StringDictionaryBuilder::<UInt16Type>::new()),
                Box::new(StringDictionaryBuilder::<UInt16Type>::new()),
            ],
        );
        let extratags_builder = Box::new(
            ListBuilder::new(extratags_struct_builder).with_field(Arc::new(Field::new(
                "item",
                DataType::Struct(Fields::from(vec![
                    Field::new("key", DataType::Dictionary(
                        Box::new(DataType::UInt16),
                        Box::new(DataType::Utf8)
                    ), false),
                    Field::new("value", DataType::Dictionary(
                        Box::new(DataType::UInt16),
                        Box::new(DataType::Utf8)
                    ), false),
                ])),
                false,
            )))
        );
        
        Self {
            osm_type: None,
            tag_config,
            osm_id_builder,
            name_builder,
            name_en_builder,
            category_builder,
            type_builder,
            extratags_builder,
            geo_builder: None,
            nds_builder: None,
            members_builder: None,
        }
    }
    
    /// Initialize builders for a specific OSM type with storage-optimized types
    pub fn initialize_for_type(&mut self, osm_type: OSMType) {
        self.osm_type = Some(osm_type.clone());

        match osm_type {
            OSMType::Node => {
                // Initialize geo builder for nodes (Float32 for better compression)
                let float_builder = Float32Builder::new();
                let geo_builder = FixedSizeListBuilder::new(float_builder, 2)
                    .with_field(Arc::new(Field::new("item", DataType::Float32, false)));
                self.geo_builder = Some(Box::new(geo_builder));
            }
            OSMType::Way => {
                // Initialize nds builder for ways (UInt64 for positive node IDs)
                let int_builder = UInt64Builder::new();
                let nds_builder = ListBuilder::new(int_builder)
                    .with_field(Arc::new(Field::new("item", DataType::UInt64, false)));
                self.nds_builder = Some(Box::new(nds_builder));
            }
            OSMType::Relation => {
                // Initialize members builder for relations (optimized member structure with dictionary encoding)
                let members_struct_builder = StructBuilder::new(
                    vec![
                        Field::new("type", DataType::UInt8, false),  // Enum: 0=node, 1=way, 2=relation
                        Field::new("ref", DataType::UInt64, false),  // UInt64 for positive IDs
                        Field::new("role", DataType::Dictionary(
                            Box::new(DataType::UInt16),
                            Box::new(DataType::Utf8)
                        ), true),
                    ],
                    vec![
                        Box::new(arrow::array::builder::UInt8Builder::new()),
                        Box::new(UInt64Builder::new()),
                        Box::new(StringDictionaryBuilder::<UInt16Type>::new()),
                    ],
                );
                let members_builder = ListBuilder::new(members_struct_builder)
                    .with_field(Arc::new(Field::new(
                        "item",
                        DataType::Struct(Fields::from(vec![
                            Field::new("type", DataType::UInt8, false),
                            Field::new("ref", DataType::UInt64, false),
                            Field::new("role", DataType::Dictionary(
                                Box::new(DataType::UInt16),
                                Box::new(DataType::Utf8)
                            ), true),
                        ])),
                        false,
                    )));
                self.members_builder = Some(Box::new(members_builder));
            }
        }
    }

    /// Extract category and type with string interning (Strategy 4: String Optimization)
    fn extract_category_and_type_optimized(&self, tags: &[(String, String)]) -> (Option<String>, Option<String>) {
        for (key, value) in tags {
            if self.tag_config.get_priority(key) > 0 {
                // Use string interning for common values
                let category = CATEGORY_INTERN.entry(key.clone())
                    .or_insert_with(|| key.clone().into())
                    .clone();
                let type_val = TYPE_INTERN.entry(value.clone())
                    .or_insert_with(|| value.clone().into())
                    .clone();

                return (Some(category.to_string()), Some(type_val.to_string()));
            }
        }
        (None, None)
    }

    /// Append a row to the builder
    pub fn append_row<'a, I, M, N>(
        &mut self,
        osm_id: i64,
        osm_type: OSMType,
        tags_iter: I,
        lat: Option<f64>,
        lon: Option<f64>,
        nds_iter: N,
        members_iter: M,
    ) -> usize
    where
        I: Iterator<Item = (String, String)>,
        N: Iterator<Item = i64>,
        M: Iterator<Item = (String, i64, Option<String>)>,
    {
        // Initialize for type if not already done
        if self.osm_type.is_none() || self.osm_type.as_ref() != Some(&osm_type) {
            self.initialize_for_type(osm_type.clone());
        }

        // Process tags using the configuration
        let tags_vec: Vec<(String, String)> = tags_iter.collect();

        // Use optimized category/type extraction with string interning
        let (category, tag_type) = self.extract_category_and_type_optimized(&tags_vec);

        let processed = process_osm_tags(
            osm_id,
            tags_vec.iter().map(|(k, v)| (k.as_str(), v.as_str())),
            &self.tag_config,
        );

        let mut est_size_bytes = 64; // Base size estimate

        // Append common fields (convert i64 to u64 for storage optimization)
        let osm_id_u64 = if osm_id >= 0 { osm_id as u64 } else { 0 }; // Handle negative IDs
        self.osm_id_builder.append_value(osm_id_u64);
        
        if let Some(name) = &processed.name {
            self.name_builder.append_value(name);
            est_size_bytes += name.len();
        } else {
            self.name_builder.append_null();
        }
        
        if let Some(name_en) = &processed.name_en {
            self.name_en_builder.append_value(name_en);
            est_size_bytes += name_en.len();
        } else {
            self.name_en_builder.append_null();
        }
        
        // Use optimized category/type with string interning
        if let Some(category) = &category {
            self.category_builder.append_value(category);
            est_size_bytes += category.len();
        } else {
            self.category_builder.append_null();
        }

        if let Some(tag_type) = &tag_type {
            self.type_builder.append_value(tag_type);
            est_size_bytes += tag_type.len();
        } else {
            self.type_builder.append_null();
        }
        
        // Append extratags with dictionary encoding
        let extratags_struct_builder = self.extratags_builder.values();
        for (key, value) in &processed.extratags {
            est_size_bytes += key.len() + value.len();

            extratags_struct_builder
                .field_builder::<StringDictionaryBuilder<UInt16Type>>(0)
                .unwrap()
                .append_value(key);

            extratags_struct_builder
                .field_builder::<StringDictionaryBuilder<UInt16Type>>(1)
                .unwrap()
                .append_value(value);

            extratags_struct_builder.append(true);
        }
        self.extratags_builder.append(true);
        
        // Append type-specific fields with storage-optimized types
        match osm_type {
            OSMType::Node => {
                if let (Some(lat), Some(lon)) = (lat, lon) {
                    if let Some(geo_builder) = &mut self.geo_builder {
                        // Convert to Float32 for better compression
                        geo_builder.values().append_value(lon as f32); // longitude first
                        geo_builder.values().append_value(lat as f32); // latitude second
                        geo_builder.append(true);
                    }
                } else {
                    if let Some(geo_builder) = &mut self.geo_builder {
                        geo_builder.append(false); // null geo
                    }
                }
            }
            OSMType::Way => {
                if let Some(nds_builder) = &mut self.nds_builder {
                    for node_ref in nds_iter {
                        // Convert to UInt64 for positive node IDs
                        let node_ref_u64 = if node_ref >= 0 { node_ref as u64 } else { 0 };
                        nds_builder.values().append_value(node_ref_u64);
                        est_size_bytes += 8; // 8 bytes per u64
                    }
                    nds_builder.append(true);
                }
            }
            OSMType::Relation => {
                if let Some(members_builder) = &mut self.members_builder {
                    for (member_type, member_ref, member_role) in members_iter {
                        let members_struct_builder = members_builder.values();

                        // Convert member type to UInt8 enum: 0=node, 1=way, 2=relation
                        let member_type_enum = match member_type.as_str() {
                            "node" => 0u8,
                            "way" => 1u8,
                            "relation" => 2u8,
                            _ => 0u8, // Default to node
                        };

                        members_struct_builder
                            .field_builder::<arrow::array::builder::UInt8Builder>(0)
                            .unwrap()
                            .append_value(member_type_enum);

                        // Convert to UInt64 for positive member IDs
                        let member_ref_u64 = if member_ref >= 0 { member_ref as u64 } else { 0 };
                        members_struct_builder
                            .field_builder::<UInt64Builder>(1)
                            .unwrap()
                            .append_value(member_ref_u64);

                        let role_len = if let Some(ref role) = member_role {
                            members_struct_builder
                                .field_builder::<StringDictionaryBuilder<UInt16Type>>(2)
                                .unwrap()
                                .append_value(role);
                            role.len()
                        } else {
                            members_struct_builder
                                .field_builder::<StringDictionaryBuilder<UInt16Type>>(2)
                                .unwrap()
                                .append_null();
                            0
                        };

                        members_struct_builder.append(true);
                        est_size_bytes += 1 + 8 + role_len; // UInt8 + UInt64 + role
                    }
                    members_builder.append(true);
                }
            }
        }
        
        est_size_bytes
    }
    
    /// Finish building and create a RecordBatch with storage-optimized schema
    pub fn finish(&mut self, osm_type: &OSMType) -> Result<RecordBatch, ArrowError> {
        let osm_id_array = Arc::new(self.osm_id_builder.finish()) as ArrayRef;
        let name_array = Arc::new(self.name_builder.finish()) as ArrayRef;
        let name_en_array = Arc::new(self.name_en_builder.finish()) as ArrayRef;
        let category_array = Arc::new(self.category_builder.finish()) as ArrayRef;
        let type_array = Arc::new(self.type_builder.finish()) as ArrayRef;
        let extratags_array = Arc::new(self.extratags_builder.finish()) as ArrayRef;

        let array_refs: Vec<ArrayRef> = match osm_type {
            OSMType::Node => {
                let geo_array = if let Some(geo_builder) = &mut self.geo_builder {
                    Arc::new(geo_builder.finish()) as ArrayRef
                } else {
                    return Err(ArrowError::InvalidArgumentError(
                        "Geo builder not initialized for node".to_string()
                    ));
                };

                vec![
                    osm_id_array,
                    name_array,
                    name_en_array,
                    category_array,
                    type_array,
                    extratags_array,
                    geo_array,
                ]
            }
            OSMType::Way => {
                let nds_array = if let Some(nds_builder) = &mut self.nds_builder {
                    Arc::new(nds_builder.finish()) as ArrayRef
                } else {
                    return Err(ArrowError::InvalidArgumentError(
                        "Nds builder not initialized for way".to_string()
                    ));
                };

                vec![
                    osm_id_array,
                    name_array,
                    name_en_array,
                    category_array,
                    type_array,
                    extratags_array,
                    nds_array,
                ]
            }
            OSMType::Relation => {
                let members_array = if let Some(members_builder) = &mut self.members_builder {
                    Arc::new(members_builder.finish()) as ArrayRef
                } else {
                    return Err(ArrowError::InvalidArgumentError(
                        "Members builder not initialized for relation".to_string()
                    ));
                };

                vec![
                    osm_id_array,
                    name_array,
                    name_en_array,
                    category_array,
                    type_array,
                    extratags_array,
                    members_array,
                ]
            }
        };

        let schema = Arc::new(get_storage_optimized_schema_for_type(osm_type));
        RecordBatch::try_new(schema, array_refs)
    }
}
