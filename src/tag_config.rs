use std::collections::HashMap;
use std::fs;
use std::path::Path;
use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use regex::Regex;
use osmpbf::{Node, DenseNode, Way, Relation};

/// Configuration for OSM data filtering and preprocessing
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FilterConfig {
    /// Whether filtering is enabled (default: true)
    pub enabled: bool,

    /// Whether to require valid geographic coordinates (lat/lon)
    pub require_coordinates: bool,

    /// Whether to require at least one name tag
    pub require_name_tags: bool,

    /// Patterns for acceptable name tags (e.g., "name", "name:*")
    /// Uses glob-like patterns where * matches any characters
    pub name_tag_patterns: Vec<String>,

    /// Additional custom filtering rules (for future extension)
    pub custom_rules: HashMap<String, String>,
}

impl Default for FilterConfig {
    fn default() -> Self {
        FilterConfig {
            enabled: true,
            require_coordinates: true,
            require_name_tags: true,
            name_tag_patterns: vec![
                "name".to_string(),
                "name:*".to_string(),
            ],
            custom_rules: HashMap::new(),
        }
    }
}

impl FilterConfig {
    /// Check if a tag key matches any of the configured name tag patterns
    pub fn matches_name_pattern(&self, tag_key: &str) -> bool {
        for pattern in &self.name_tag_patterns {
            if pattern == tag_key {
                return true;
            }

            // Handle glob-like patterns (simple * wildcard support)
            if pattern.contains('*') {
                let regex_pattern = pattern.replace('*', ".*");
                if let Ok(regex) = Regex::new(&format!("^{}$", regex_pattern)) {
                    if regex.is_match(tag_key) {
                        return true;
                    }
                }
            }
        }
        false
    }

    /// Check if the given tags contain at least one name tag
    pub fn has_name_tags<'a, I>(&self, tags: I) -> bool
    where
        I: Iterator<Item = (&'a str, &'a str)>,
    {
        for (key, _value) in tags {
            if self.matches_name_pattern(key) {
                return true;
            }
        }
        false
    }
}

/// Configuration for OSM tag priority and processing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TagConfig {
    /// Priority mapping for top-level OSM tags
    /// Higher numbers indicate higher priority
    pub tag_priorities: HashMap<String, u32>,

    /// Tags that should be excluded from extratags
    /// These are typically handled by dedicated fields
    pub excluded_tags: Vec<String>,

    /// Default priority for tags not explicitly configured
    pub default_priority: u32,

    /// Filtering configuration
    #[serde(default)]
    pub filter: FilterConfig,
}

impl Default for TagConfig {
    fn default() -> Self {
        let mut tag_priorities = HashMap::new();

        // High priority tags (infrastructure and primary features)
        tag_priorities.insert("highway".to_string(), 100);
        tag_priorities.insert("railway".to_string(), 95);
        tag_priorities.insert("waterway".to_string(), 90);
        tag_priorities.insert("aeroway".to_string(), 85);
        tag_priorities.insert("power".to_string(), 80);

        // Medium-high priority tags (land use and areas)
        tag_priorities.insert("landuse".to_string(), 75);
        tag_priorities.insert("natural".to_string(), 70);
        tag_priorities.insert("leisure".to_string(), 65);
        tag_priorities.insert("amenity".to_string(), 60);
        tag_priorities.insert("shop".to_string(), 55);
        tag_priorities.insert("tourism".to_string(), 50);

        // Medium priority tags (buildings and structures)
        tag_priorities.insert("building".to_string(), 45);
        tag_priorities.insert("man_made".to_string(), 40);
        tag_priorities.insert("historic".to_string(), 35);
        tag_priorities.insert("military".to_string(), 30);

        // Lower priority tags (administrative and boundaries)
        tag_priorities.insert("boundary".to_string(), 25);
        tag_priorities.insert("admin_level".to_string(), 20);
        tag_priorities.insert("place".to_string(), 15);

        // Specialized tags
        tag_priorities.insert("craft".to_string(), 10);
        tag_priorities.insert("office".to_string(), 8);
        tag_priorities.insert("emergency".to_string(), 5);

        let excluded_tags = vec![
            "name".to_string(),
            "name:en".to_string(),
            "changeset".to_string(),
            "timestamp".to_string(),
            "uid".to_string(),
            "user".to_string(),
            "version".to_string(),
            "visible".to_string(),
            "source".to_string(),
        ];

        TagConfig {
            tag_priorities,
            excluded_tags,
            default_priority: 1,
            filter: FilterConfig::default(),
        }
    }
}

impl TagConfig {
    /// Load configuration from a TOML file
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = fs::read_to_string(path.as_ref())
            .with_context(|| format!("Failed to read config file: {:?}", path.as_ref()))?;
        
        let config: TagConfig = toml::from_str(&content)
            .with_context(|| "Failed to parse TOML configuration")?;
        
        Ok(config)
    }
    
    /// Save configuration to a TOML file
    pub fn to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = toml::to_string_pretty(self)
            .with_context(|| "Failed to serialize configuration to TOML")?;
        
        fs::write(path.as_ref(), content)
            .with_context(|| format!("Failed to write config file: {:?}", path.as_ref()))?;
        
        Ok(())
    }
    
    /// Get the priority for a given tag key
    pub fn get_priority(&self, tag_key: &str) -> u32 {
        self.tag_priorities.get(tag_key).copied().unwrap_or(self.default_priority)
    }
    
    /// Check if a tag should be excluded from extratags
    pub fn is_excluded(&self, tag_key: &str) -> bool {
        self.excluded_tags.contains(&tag_key.to_string())
    }
    
    /// Find the highest priority tag from a collection of tags
    /// Returns (category, type) tuple where category is the tag key and type is the tag value
    /// Only considers tags that are not excluded (like name, changeset, etc.)
    pub fn extract_category_type<'a, I>(&self, tags: I) -> Option<(String, String)>
    where
        I: Iterator<Item = (&'a str, &'a str)>,
    {
        let mut best_priority = 0;
        let mut best_tag: Option<(String, String)> = None;

        for (key, value) in tags {
            // Skip excluded tags for category/type extraction
            if self.is_excluded(key) {
                continue;
            }

            let priority = self.get_priority(key);
            if priority > best_priority {
                best_priority = priority;
                best_tag = Some((key.to_string(), value.to_string()));
            }
        }

        best_tag
    }
    
    /// Build extratags map excluding handled tags
    pub fn build_extratags<'a, I>(&self, tags: I, category_key: Option<&str>) -> HashMap<String, String>
    where
        I: Iterator<Item = (&'a str, &'a str)>,
    {
        let mut extratags = HashMap::new();
        
        for (key, value) in tags {
            // Skip if this tag is excluded
            if self.is_excluded(key) {
                continue;
            }
            
            // Skip if this is the category tag we already extracted
            if let Some(cat_key) = category_key {
                if key == cat_key {
                    continue;
                }
            }
            
            extratags.insert(key.to_string(), value.to_string());
        }
        
        extratags
    }
}

/// Processed OSM tag information
#[derive(Debug, Clone)]
pub struct ProcessedTags {
    pub osm_id: i64,
    pub name: Option<String>,
    pub name_en: Option<String>,
    pub category: Option<String>,
    pub tag_type: Option<String>,
    pub extratags: HashMap<String, String>,
}

/// Process OSM tags according to the configuration
pub fn process_osm_tags<'a, I>(
    osm_id: i64,
    tags: I,
    config: &TagConfig,
) -> ProcessedTags
where
    I: Iterator<Item = (&'a str, &'a str)> + Clone,
{
    let tags_vec: Vec<(&str, &str)> = tags.collect();
    
    // Extract name fields
    let name = tags_vec.iter()
        .find(|(k, _)| *k == "name")
        .map(|(_, v)| v.to_string());
    
    let name_en = tags_vec.iter()
        .find(|(k, _)| *k == "name:en")
        .map(|(_, v)| v.to_string());
    
    // Extract category and type from highest priority tag
    let (category, tag_type) = if let Some((cat, typ)) = config.extract_category_type(tags_vec.iter().copied()) {
        (Some(cat.clone()), Some(typ))
    } else {
        (None, None)
    };
    
    // Build extratags excluding handled fields
    let extratags = config.build_extratags(
        tags_vec.iter().copied(),
        category.as_deref(),
    );
    
    ProcessedTags {
        osm_id,
        name,
        name_en,
        category,
        tag_type,
        extratags,
    }
}

/// Statistics for filtering operations
#[derive(Debug, Default, Clone)]
pub struct FilterStats {
    pub total_processed: u64,
    pub filtered_out: u64,
    pub filtered_no_coordinates: u64,
    pub filtered_no_name_tags: u64,
    pub filtered_custom_rules: u64,
}

impl FilterStats {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn record_processed(&mut self) {
        self.total_processed += 1;
    }

    pub fn record_filtered_no_coordinates(&mut self) {
        self.filtered_out += 1;
        self.filtered_no_coordinates += 1;
    }

    pub fn record_filtered_no_name_tags(&mut self) {
        self.filtered_out += 1;
        self.filtered_no_name_tags += 1;
    }

    pub fn record_filtered_custom_rules(&mut self) {
        self.filtered_out += 1;
        self.filtered_custom_rules += 1;
    }

    pub fn accepted(&self) -> u64 {
        self.total_processed - self.filtered_out
    }

    pub fn acceptance_rate(&self) -> f64 {
        if self.total_processed == 0 {
            0.0
        } else {
            self.accepted() as f64 / self.total_processed as f64
        }
    }
}

/// OSM element filtering functions
pub mod filter {
    use super::*;

    /// Check if a node should be included based on filtering rules
    pub fn should_include_node(node: &Node, config: &FilterConfig) -> bool {
        if !config.enabled {
            return true;
        }

        // Check coordinates requirement
        if config.require_coordinates {
            // For nodes, we always have coordinates, but check if they're valid
            let lat = node.lat();
            let lon = node.lon();
            if lat < -90.0 || lat > 90.0 || lon < -180.0 || lon > 180.0 {
                return false;
            }
        }

        // Check name tags requirement
        if config.require_name_tags {
            if !config.has_name_tags(node.tags()) {
                return false;
            }
        }

        // TODO: Add custom rules processing here

        true
    }

    /// Check if a dense node should be included based on filtering rules
    pub fn should_include_dense_node(node: &DenseNode, config: &FilterConfig) -> bool {
        if !config.enabled {
            return true;
        }

        // Check coordinates requirement
        if config.require_coordinates {
            let lat = node.lat();
            let lon = node.lon();
            if lat < -90.0 || lat > 90.0 || lon < -180.0 || lon > 180.0 {
                return false;
            }
        }

        // Check name tags requirement
        if config.require_name_tags {
            if !config.has_name_tags(node.tags()) {
                return false;
            }
        }

        // TODO: Add custom rules processing here

        true
    }

    /// Check if a way should be included based on filtering rules
    pub fn should_include_way(way: &Way, config: &FilterConfig) -> bool {
        if !config.enabled {
            return true;
        }

        // Ways don't have direct coordinates, so skip coordinate check

        // Check name tags requirement
        if config.require_name_tags {
            if !config.has_name_tags(way.tags()) {
                return false;
            }
        }

        // TODO: Add custom rules processing here

        true
    }

    /// Check if a relation should be included based on filtering rules
    pub fn should_include_relation(relation: &Relation, config: &FilterConfig) -> bool {
        if !config.enabled {
            return true;
        }

        // Relations don't have direct coordinates, so skip coordinate check

        // Check name tags requirement
        if config.require_name_tags {
            if !config.has_name_tags(relation.tags()) {
                return false;
            }
        }

        // TODO: Add custom rules processing here

        true
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_default_config() {
        let config = TagConfig::default();
        
        // Test priority ordering
        assert!(config.get_priority("highway") > config.get_priority("amenity"));
        assert!(config.get_priority("amenity") > config.get_priority("building"));
        assert!(config.get_priority("building") > config.get_priority("place"));
    }
    
    #[test]
    fn test_category_extraction() {
        let config = TagConfig::default();
        
        let tags = vec![
            ("building", "yes"),
            ("amenity", "restaurant"),
            ("name", "Test Restaurant"),
        ];
        
        let (category, tag_type) = config.extract_category_type(tags.iter().map(|(k, v)| (*k, *v))).unwrap();
        
        // amenity should win over building due to higher priority
        assert_eq!(category, "amenity");
        assert_eq!(tag_type, "restaurant");
    }
    
    #[test]
    fn test_extratags_building() {
        let config = TagConfig::default();
        
        let tags = vec![
            ("building", "yes"),
            ("amenity", "restaurant"),
            ("name", "Test Restaurant"),
            ("cuisine", "italian"),
            ("opening_hours", "Mo-Su 10:00-22:00"),
        ];
        
        let extratags = config.build_extratags(
            tags.iter().map(|(k, v)| (*k, *v)),
            Some("amenity"), // amenity was selected as category
        );
        
        // Should include building, cuisine, opening_hours but not name or amenity
        assert!(extratags.contains_key("building"));
        assert!(extratags.contains_key("cuisine"));
        assert!(extratags.contains_key("opening_hours"));
        assert!(!extratags.contains_key("name"));
        assert!(!extratags.contains_key("amenity"));
    }
    
    #[test]
    fn test_process_osm_tags() {
        let config = TagConfig::default();
        
        let tags = vec![
            ("building", "yes"),
            ("amenity", "restaurant"),
            ("name", "Test Restaurant"),
            ("name:en", "Test Restaurant EN"),
            ("cuisine", "italian"),
        ];
        
        let processed = process_osm_tags(
            12345,
            tags.iter().map(|(k, v)| (*k, *v)),
            &config,
        );
        
        assert_eq!(processed.osm_id, 12345);
        assert_eq!(processed.name, Some("Test Restaurant".to_string()));
        assert_eq!(processed.name_en, Some("Test Restaurant EN".to_string()));
        assert_eq!(processed.category, Some("amenity".to_string()));
        assert_eq!(processed.tag_type, Some("restaurant".to_string()));
        assert!(processed.extratags.contains_key("building"));
        assert!(processed.extratags.contains_key("cuisine"));
    }

    #[test]
    fn test_source_tag_exclusion() {
        let config = TagConfig::default();

        let tags = vec![
            ("highway", "primary"),
            ("name", "Main Street"),
            ("source", "Bing"),
            ("maxspeed", "50"),
        ];

        let processed = process_osm_tags(
            12345,
            tags.iter().map(|(k, v)| (*k, *v)),
            &config,
        );

        // Verify that source tag is excluded from extratags
        assert!(!processed.extratags.contains_key("source"));
        // But other tags should still be included
        assert!(processed.extratags.contains_key("maxspeed"));
        // And category/type should work normally
        assert_eq!(processed.category, Some("highway".to_string()));
        assert_eq!(processed.tag_type, Some("primary".to_string()));
    }

    #[test]
    fn test_filter_config_name_patterns() {
        let config = FilterConfig::default();

        // Test exact match
        assert!(config.matches_name_pattern("name"));

        // Test wildcard patterns
        assert!(config.matches_name_pattern("name:en"));
        assert!(config.matches_name_pattern("name:zh"));
        assert!(config.matches_name_pattern("name:fr"));

        // Test non-matching patterns
        assert!(!config.matches_name_pattern("highway"));
        assert!(!config.matches_name_pattern("building"));
    }

    #[test]
    fn test_filter_config_has_name_tags() {
        let config = FilterConfig::default();

        // Test with name tag
        let tags_with_name = vec![("highway", "primary"), ("name", "Main Street")];
        assert!(config.has_name_tags(tags_with_name.iter().map(|(k, v)| (*k, *v))));

        // Test with multilingual name tag
        let tags_with_name_en = vec![("highway", "primary"), ("name:en", "Main Street")];
        assert!(config.has_name_tags(tags_with_name_en.iter().map(|(k, v)| (*k, *v))));

        // Test without name tags
        let tags_without_name = vec![("highway", "primary"), ("maxspeed", "50")];
        assert!(!config.has_name_tags(tags_without_name.iter().map(|(k, v)| (*k, *v))));
    }

    #[test]
    fn test_filter_stats() {
        let mut stats = FilterStats::new();

        // Test initial state
        assert_eq!(stats.total_processed, 0);
        assert_eq!(stats.filtered_out, 0);
        assert_eq!(stats.accepted(), 0);
        assert_eq!(stats.acceptance_rate(), 0.0);

        // Test recording processed items
        stats.record_processed();
        stats.record_processed();
        stats.record_filtered_no_name_tags();

        assert_eq!(stats.total_processed, 2);
        assert_eq!(stats.filtered_out, 1);
        assert_eq!(stats.accepted(), 1);
        assert_eq!(stats.acceptance_rate(), 0.5);
    }
}
