use std::sync::Arc;
use std::time::Instant;
use std::collections::HashMap;
use anyhow::{Context, Result};
use log::{debug, info};
use osmpbf::{DenseNode, Node, Relation, Way};

use crate::lancedb_connection::LanceDBManager;
use crate::optimized_arrow::OptimizedOSMArrowBuilder;
use crate::storage_optimized_schema::{get_storage_optimized_table_name, CompressionConfig};
use crate::osm_arrow::OSMType;
use crate::tag_config::{TagConfig, FilterStats, filter};
use crate::util::ARGS;

/// Optimized LanceDB sink for OSM data using the new schema
pub struct OptimizedLanceDBSink {
    pub osm_type: OSMType,
    db_manager: Arc<LanceDBManager>,
    osm_builder: OptimizedOSMArrowBuilder,
    tag_config: TagConfig,

    // Batching control
    estimated_record_batch_bytes: usize,
    target_record_batch_bytes: usize,
    pub last_write_cycle: Instant,

    // Batch accumulation for efficient writes
    pending_batches: Vec<arrow::record_batch::RecordBatch>,
    target_batch_count: usize,

    // Filtering statistics
    pub filter_stats: FilterStats,
}

impl OptimizedLanceDBSink {
    /// Create a new optimized LanceDB sink for the specified OSM type
    pub fn new(osm_type: OSMType, db_manager: Arc<LanceDBManager>, tag_config: TagConfig) -> Result<Self> {
        let _args = ARGS.get().unwrap();

        // Get compression config for optimized batch sizes
        let compression_config = CompressionConfig::for_osm_type(&osm_type);

        // Optimize batch sizes based on data type (Strategy 3)
        let (target_batch_bytes, target_batch_count) = match osm_type {
            OSMType::Node => (32 * 1024 * 1024, 5),    // 32MB batches, fewer fragments
            OSMType::Way => (64 * 1024 * 1024, 3),     // 64MB batches for larger records
            OSMType::Relation => (16 * 1024 * 1024, 2), // 16MB batches for complex records
        };

        info!(
            "Creating optimized sink for {:?} with {}MB target batch size, {} batch count, {} compression",
            osm_type,
            target_batch_bytes / (1024 * 1024),
            target_batch_count,
            compression_config.algorithm
        );

        Ok(OptimizedLanceDBSink {
            osm_type,
            db_manager,
            osm_builder: OptimizedOSMArrowBuilder::new(tag_config.clone()),
            tag_config,
            estimated_record_batch_bytes: 0,
            target_record_batch_bytes: target_batch_bytes,
            last_write_cycle: Instant::now(),
            pending_batches: Vec::new(),
            target_batch_count,
            filter_stats: FilterStats::new(),
        })
    }
    
    /// Check if we should cycle (write current batch and start new one)
    pub fn should_cycle(&self) -> bool {
        self.estimated_record_batch_bytes >= self.target_record_batch_bytes
    }

    /// Increment counters and cycle if needed
    pub async fn increment_and_cycle(&mut self) -> Result<()> {
        if self.should_cycle() {
            self.cycle().await?;
        }
        Ok(())
    }
    
    /// Cycle the sink (finish current batch and start new one)
    pub async fn cycle(&mut self) -> Result<()> {
        if self.estimated_record_batch_bytes == 0 {
            return Ok(()); // Nothing to cycle
        }
        
        debug!(
            "Cycling optimized sink for {:?}, estimated bytes: {}",
            self.osm_type, self.estimated_record_batch_bytes
        );
        
        // Finish current batch
        let batch = self.osm_builder.finish(&self.osm_type)
            .with_context(|| format!("Failed to finish batch for {:?}", self.osm_type))?;
        
        self.pending_batches.push(batch);
        
        // Reset for next batch
        self.osm_builder = OptimizedOSMArrowBuilder::new(self.tag_config.clone());
        self.estimated_record_batch_bytes = 0;
        self.last_write_cycle = Instant::now();
        // Note: We don't reset filter_stats here as they accumulate across batches
        
        // Write to LanceDB if we have enough batches
        if self.pending_batches.len() >= self.target_batch_count {
            self.write_to_lancedb().await?;
        }
        
        Ok(())
    }
    
    /// Write accumulated batches to LanceDB with compression
    pub async fn write_to_lancedb(&mut self) -> Result<()> {
        if self.pending_batches.is_empty() {
            return Ok(());
        }

        let table_name = get_storage_optimized_table_name(&self.osm_type);
        let batch_count = self.pending_batches.len();

        info!(
            "Writing {} batches to storage-optimized table: {}",
            batch_count, table_name
        );
        
        // Ensure table exists
        self.db_manager.ensure_optimized_table_exists(&self.osm_type).await?;

        // Use compressed writing method
        let batches_to_write: Vec<_> = self.pending_batches.drain(..).collect();
        self.db_manager.add_optimized_data_compressed(&self.osm_type, batches_to_write).await
            .with_context(|| format!("Failed to write compressed batches to table {}", table_name))?;

        info!("Successfully wrote {} compressed batches to {}", batch_count, table_name);
        Ok(())
    }
    
    /// Flush any remaining data
    pub async fn flush(&mut self) -> Result<()> {
        // Cycle any remaining data
        if self.estimated_record_batch_bytes > 0 {
            self.cycle().await?;
        }

        // Write any remaining batches
        if !self.pending_batches.is_empty() {
            self.write_to_lancedb().await?;
        }

        Ok(())
    }

    /// Alias for flush() to maintain compatibility with legacy interface
    pub async fn finish(&mut self) -> Result<()> {
        self.flush().await
    }

    /// Get a copy of the current filter statistics
    pub fn get_filter_stats(&self) -> FilterStats {
        self.filter_stats.clone()
    }
    
    /// Add a node to the sink
    pub fn add_node(&mut self, node: &Node) {
        self.filter_stats.record_processed();

        // Check if filtering is bypassed via --full flag
        let args = ARGS.get().unwrap();
        if args.full {
            let est_size_bytes = self.osm_builder.append_row(
                node.id(),
                OSMType::Node,
                node.tags().map(|(key, value)| (key.to_string(), value.to_string())),
                Some(node.lat()),
                Some(node.lon()),
                std::iter::empty(),
                std::iter::empty(),
            );
            self.estimated_record_batch_bytes += est_size_bytes;
            return;
        }

        // Apply filtering rules
        if !filter::should_include_node(node, &self.tag_config.filter) {
            // Determine the specific reason for filtering
            if self.tag_config.filter.require_coordinates {
                let lat = node.lat();
                let lon = node.lon();
                if lat < -90.0 || lat > 90.0 || lon < -180.0 || lon > 180.0 {
                    self.filter_stats.record_filtered_no_coordinates();
                    return;
                }
            }

            if self.tag_config.filter.require_name_tags {
                if !self.tag_config.filter.has_name_tags(node.tags()) {
                    self.filter_stats.record_filtered_no_name_tags();
                    return;
                }
            }

            // If we get here, it was filtered by custom rules
            self.filter_stats.record_filtered_custom_rules();
            return;
        }

        // Node passed filtering, add it to the builder
        let est_size_bytes = self.osm_builder.append_row(
            node.id(),
            OSMType::Node,
            node.tags().map(|(key, value)| (key.to_string(), value.to_string())),
            Some(node.lat()),
            Some(node.lon()),
            std::iter::empty(),
            std::iter::empty(),
        );
        self.estimated_record_batch_bytes += est_size_bytes;
    }
    
    /// Add a dense node to the sink
    pub fn add_dense_node(&mut self, node: &DenseNode) {
        self.filter_stats.record_processed();

        // Check if filtering is bypassed via --full flag
        let args = ARGS.get().unwrap();
        if args.full {
            let est_size_bytes = self.osm_builder.append_row(
                node.id(),
                OSMType::Node,
                node.tags().map(|(key, value)| (key.to_string(), value.to_string())),
                Some(node.lat()),
                Some(node.lon()),
                std::iter::empty(),
                std::iter::empty(),
            );
            self.estimated_record_batch_bytes += est_size_bytes;
            return;
        }

        // Apply filtering rules
        if !filter::should_include_dense_node(node, &self.tag_config.filter) {
            // Determine the specific reason for filtering
            if self.tag_config.filter.require_coordinates {
                let lat = node.lat();
                let lon = node.lon();
                if lat < -90.0 || lat > 90.0 || lon < -180.0 || lon > 180.0 {
                    self.filter_stats.record_filtered_no_coordinates();
                    return;
                }
            }

            if self.tag_config.filter.require_name_tags {
                if !self.tag_config.filter.has_name_tags(node.tags()) {
                    self.filter_stats.record_filtered_no_name_tags();
                    return;
                }
            }

            // If we get here, it was filtered by custom rules
            self.filter_stats.record_filtered_custom_rules();
            return;
        }

        // Dense node passed filtering, add it to the builder
        let est_size_bytes = self.osm_builder.append_row(
            node.id(),
            OSMType::Node,
            node.tags().map(|(key, value)| (key.to_string(), value.to_string())),
            Some(node.lat()),
            Some(node.lon()),
            std::iter::empty(),
            std::iter::empty(),
        );
        self.estimated_record_batch_bytes += est_size_bytes;
    }
    
    /// Add a way to the sink
    pub fn add_way(&mut self, way: &Way) {
        self.filter_stats.record_processed();

        // Check if filtering is bypassed via --full flag
        let args = ARGS.get().unwrap();
        if args.full {
            let est_size_bytes = self.osm_builder.append_row(
                way.id(),
                OSMType::Way,
                way.tags().map(|(key, value)| (key.to_string(), value.to_string())),
                None,
                None,
                way.refs(),
                std::iter::empty(),
            );
            self.estimated_record_batch_bytes += est_size_bytes;
            return;
        }

        // Apply filtering rules
        if !filter::should_include_way(way, &self.tag_config.filter) {
            // Ways don't have coordinates, so only check name tags
            if self.tag_config.filter.require_name_tags {
                if !self.tag_config.filter.has_name_tags(way.tags()) {
                    self.filter_stats.record_filtered_no_name_tags();
                    return;
                }
            }

            // If we get here, it was filtered by custom rules
            self.filter_stats.record_filtered_custom_rules();
            return;
        }

        // Way passed filtering, add it to the builder
        let est_size_bytes = self.osm_builder.append_row(
            way.id(),
            OSMType::Way,
            way.tags().map(|(key, value)| (key.to_string(), value.to_string())),
            None,
            None,
            way.refs(),
            std::iter::empty(),
        );
        self.estimated_record_batch_bytes += est_size_bytes;
    }
    
    /// Add a relation to the sink
    pub fn add_relation(&mut self, relation: &Relation) {
        self.filter_stats.record_processed();

        // Check if filtering is bypassed via --full flag
        let args = ARGS.get().unwrap();
        if args.full {
            let members_iter = relation.members().map(|member| {
                let member_type = match member.member_type {
                    osmpbf::RelMemberType::Node => "node".to_string(),
                    osmpbf::RelMemberType::Way => "way".to_string(),
                    osmpbf::RelMemberType::Relation => "relation".to_string(),
                };
                let member_role = match member.role() {
                    Ok(role) if !role.is_empty() => Some(role.to_string()),
                    _ => None,
                };
                (member_type, member.member_id, member_role)
            });

            let est_size_bytes = self.osm_builder.append_row(
                relation.id(),
                OSMType::Relation,
                relation.tags().map(|(key, value)| (key.to_string(), value.to_string())),
                None,
                None,
                std::iter::empty(),
                members_iter,
            );
            self.estimated_record_batch_bytes += est_size_bytes;
            return;
        }

        // Apply filtering rules
        if !filter::should_include_relation(relation, &self.tag_config.filter) {
            // Relations don't have coordinates, so only check name tags
            if self.tag_config.filter.require_name_tags {
                if !self.tag_config.filter.has_name_tags(relation.tags()) {
                    self.filter_stats.record_filtered_no_name_tags();
                    return;
                }
            }

            // If we get here, it was filtered by custom rules
            self.filter_stats.record_filtered_custom_rules();
            return;
        }

        // Relation passed filtering, add it to the builder
        let members_iter = relation.members().map(|member| {
            let member_type = match member.member_type {
                osmpbf::RelMemberType::Node => "node".to_string(),
                osmpbf::RelMemberType::Way => "way".to_string(),
                osmpbf::RelMemberType::Relation => "relation".to_string(),
            };
            let member_role = match member.role() {
                Ok(role) if !role.is_empty() => Some(role.to_string()),
                _ => None,
            };
            (member_type, member.member_id, member_role)
        });

        let est_size_bytes = self.osm_builder.append_row(
            relation.id(),
            OSMType::Relation,
            relation.tags().map(|(key, value)| (key.to_string(), value.to_string())),
            None,
            None,
            std::iter::empty(),
            members_iter,
        );
        self.estimated_record_batch_bytes += est_size_bytes;
    }
}

/// Store for managing multiple optimized sinks
pub type OptimizedLanceDBSinkpoolStore = std::collections::HashMap<OSMType, Arc<std::sync::Mutex<Vec<OptimizedLanceDBSink>>>>;

/// Create optimized sinkpools for all OSM types
pub fn create_optimized_sinkpools(
    db_manager: Arc<LanceDBManager>,
    tag_config: TagConfig,
) -> Result<OptimizedLanceDBSinkpoolStore> {
    use std::collections::HashMap;
    use std::sync::Mutex;
    
    let mut sinkpools = HashMap::new();
    
    for osm_type in [OSMType::Node, OSMType::Way, OSMType::Relation] {
        let sinks = vec![OptimizedLanceDBSink::new(osm_type.clone(), db_manager.clone(), tag_config.clone())?];
        sinkpools.insert(osm_type, Arc::new(Mutex::new(sinks)));
    }
    
    Ok(sinkpools)
}

/// Finish all optimized sinks and flush remaining data
pub async fn finish_optimized_lancedb_sinks(
    sinkpools: Arc<OptimizedLanceDBSinkpoolStore>,
    force_flush: bool,
) -> Result<()> {
    info!("Finishing optimized LanceDB sinks (force_flush: {})", force_flush);
    
    for (osm_type, sinkpool) in sinkpools.iter() {
        let mut sinks = sinkpool.lock().unwrap();
        for sink in sinks.iter_mut() {
            if force_flush {
                sink.flush().await?;
            }
        }
        info!("Finished optimized sinks for {:?}", osm_type);
    }
    
    info!("All optimized LanceDB sinks finished");
    Ok(())
}

/// Collect filtering statistics from all sinks
pub fn collect_filter_stats(sinkpools: Arc<OptimizedLanceDBSinkpoolStore>) -> HashMap<OSMType, FilterStats> {
    let mut stats_map = HashMap::new();

    for (osm_type, sinkpool) in sinkpools.iter() {
        let sinks = sinkpool.lock().unwrap();
        let mut combined_stats = FilterStats::new();

        for sink in sinks.iter() {
            let sink_stats = sink.get_filter_stats();
            combined_stats.total_processed += sink_stats.total_processed;
            combined_stats.filtered_out += sink_stats.filtered_out;
            combined_stats.filtered_no_coordinates += sink_stats.filtered_no_coordinates;
            combined_stats.filtered_no_name_tags += sink_stats.filtered_no_name_tags;
            combined_stats.filtered_custom_rules += sink_stats.filtered_custom_rules;
        }

        stats_map.insert(osm_type.clone(), combined_stats);
    }

    stats_map
}
