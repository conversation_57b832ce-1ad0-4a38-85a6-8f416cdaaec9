[package]
name = "osm-pbf-lancedb"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.*"
arrow = "55.*"
bytes = "1.*"
clap = { version = "4.*", features = ["derive"] }
env_logger = "0.11.*"
flate2 = { version = "1.*", features = ["zlib-ng"], default-features = false }
futures = "0.3.*"
futures-util = "0.3.*"
log = "0.4.*"
object_store = {version = "0.10.2", features = ["aws"] }
regex = "1.*"
# TODO - move this upstream once https://github.com/b-r-u/osmpbf/pull/48 is landed
osmpbf = { version = "0.3.*", features = ["zlib-ng", "async"], default-features = false, git = "https://github.com/brad-richardson/osmpbf.git", branch = "async-blob-reader" }
lancedb = "0.21.*"
serde = { version = "1.*", features = ["derive"] }
serde_json = "1.*"
sysinfo = "0.31.*"
toml = "0.8.*"
tempfile = "3.20.0"
tokio = { version =  "1.*", features = ["rt", "rt-multi-thread", "io-util"] }
tokio-util = {version = "0.7.*", features = ["io-util"] }
url = "2.*"
once_cell = "1.21.3"
dashmap = "6.1.0"

[dev-dependencies]
criterion = { version = "0.5.*", features = ["async_futures", "async_tokio"] }
tokio-test = "0.4.*"

[lib]
name = "osm_pbf_lancedb"
path = "src/lib.rs"

[[bin]]
name = "osm-pbf-lancedb"
path = "src/main.rs"

[[bench]]
name = "benchmark"
harness = false
