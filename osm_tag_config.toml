# OSM Tag Priority Configuration
# Higher numbers indicate higher priority when multiple top-level tags exist

# Default priority for tags not explicitly configured
default_priority = 1

# Tags to exclude from extratags (handled by dedicated fields)
excluded_tags = [
    "name",
    "name:en",
    "changeset",
    "timestamp",
    "uid",
    "user",
    "version",
    "visible",
    "source"
]

# OSM Data Filtering Configuration
[filter]
# Whether filtering is enabled (set to false to disable all filtering)
enabled = true

# Whether to require valid geographic coordinates (latitude and longitude)
# Only applies to nodes; ways and relations don't have direct coordinates
require_coordinates = true

# Whether to require at least one name tag for inclusion
require_name_tags = true

# Patterns for acceptable name tags (supports glob-like * wildcard)
# Examples: "name" matches exactly, "name:*" matches "name:en", "name:zh", etc.
name_tag_patterns = [
    "name",
    "name:*"
]

# Custom filtering rules (for future extension)
[filter.custom_rules]
# Example: min_tag_count = "2"  # Require at least 2 tags
# Add custom rules here as needed

# Tag priorities - higher values take precedence
[tag_priorities]

# Infrastructure and transportation (highest priority)
highway = 100
railway = 95
waterway = 90
aeroway = 85
power = 80

# Land use and natural features (medium-high priority)
landuse = 75
natural = 70
leisure = 65
amenity = 60
shop = 55
tourism = 50

# Buildings and structures (medium priority)
building = 45
man_made = 40
historic = 35
military = 30

# Administrative and boundaries (lower priority)
boundary = 25
admin_level = 20
place = 15

# Specialized categories (lowest priority)
craft = 10
office = 8
emergency = 5

# Additional common tags
barrier = 12
public_transport = 58
sport = 42
healthcare = 52
education = 48
